import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { User, UserDocument } from "../models/User";
import MailchimpService, { AudienceIdEnum } from "../external-services/mailchimpService";
import NotificationSettingsService from "../services/notificationSettingsService";
import { EmailNotificationSettingEnum } from "../models/NotificationSettings";

interface ProcessingStats {
  totalProcessed: number;
  usersFound: number;
  usersUpdated: number;
  usersNotFound: number;
  errors: number;
}

interface ScriptOptions {
  dryRun: boolean;
}

class SyncMailchimpUnsubscribersScript extends ScriptRunner {
  scriptName = "sync-mailchimp-unsubscribers";
  private options: ScriptOptions;

  async processFn(): Promise<void> {
    // Initialize options from command line arguments
    this.options = {
      dryRun: process.argv.includes("--dry-run")
    };

    logger.info("Starting sync of Mailchimp unsubscribers to update notification settings...", {
      module: `script:${this.scriptName}`,
      data: { options: this.options }
    });

    if (this.options.dryRun) {
      logger.info("Running in dry-run mode, no changes will be applied!", {
        module: `script:${this.scriptName}`
      });
    }

    for (const audience of [AudienceIdEnum.WEALTHYHOOD, AudienceIdEnum.WEALTHYBITES]) {
      await this.processAudience(audience);
    }

    logger.info("Finished syncing Mailchimp unsubscribers!", {
      module: `script:${this.scriptName}`
    });
  }

  async processAudience(audience: AudienceIdEnum) {
    logger.info(`Processing audience: ${audience}`, {
      module: `script:${this.scriptName}`,
      method: "processAudience",
      data: { audience }
    });

    // Step 1: Fetch all unsubscribed members from Mailchimp
    let offset = 0;
    const unsubscribedMemberEmails = new Set<string>();

    logger.info(`Fetching unsubscribed members from Mailchimp for audience: ${audience}`, {
      module: `script:${this.scriptName}`,
      method: "processAudience"
    });

    while (true) {
      const unsubscribedMembers = await MailchimpService.getUnsubscribedMembers(audience, {
        offset: offset
      });

      if (unsubscribedMembers.length === 0) {
        break;
      }

      unsubscribedMembers.forEach((member) => {
        unsubscribedMemberEmails.add(member.email);
      });

      offset += unsubscribedMembers.length;

      logger.info(
        `Fetched ${unsubscribedMembers.length} unsubscribed members (total: ${unsubscribedMemberEmails.size})`,
        {
          module: `script:${this.scriptName}`,
          method: "processAudience",
          data: { audience, offset, batchSize: unsubscribedMembers.length }
        }
      );
    }

    logger.info(`Found ${unsubscribedMemberEmails.size} total unsubscribed members for audience: ${audience}`, {
      module: `script:${this.scriptName}`,
      method: "processAudience",
      data: { audience, totalUnsubscribed: unsubscribedMemberEmails.size }
    });

    // Step 2: Process users and update notification settings
    const stats: ProcessingStats = {
      totalProcessed: 0,
      usersFound: 0,
      usersUpdated: 0,
      usersNotFound: 0,
      errors: 0
    };

    await User.find({})
      .cursor()
      .addCursorFlag("noCursorTimeout", true)
      .eachAsync(
        async (users: UserDocument[]) => {
          const updatePromises = users.map(async (user) => {
            stats.totalProcessed++;

            if (!unsubscribedMemberEmails.has(user.email)) {
              stats.usersNotFound++;
              return;
            }

            stats.usersFound++;

            if (this.options.dryRun) {
              return;
            }

            try {
              await Promise.all([
                NotificationSettingsService.updateNotificationSetting(
                  user,
                  EmailNotificationSettingEnum.PROMOTIONAL,
                  false
                ),
                await NotificationSettingsService.updateNotificationSetting(
                  user,
                  EmailNotificationSettingEnum.WEALTHYBITES,
                  false
                )
              ]);
              stats.usersUpdated++;
            } catch (error) {
              stats.errors++;
              logger.error(`Failed to update notification setting for user: ${user.email}`, {
                module: `script:${this.scriptName}`,
                method: "processAudience",
                data: {
                  audience,
                  userEmail: user.email,
                  error
                }
              });
            }
          });

          await Promise.all(updatePromises);

          // Log progress every batch
          if (stats.totalProcessed % 100 === 0) {
            logger.info(`Progress update for audience ${audience}`, {
              module: `script:${this.scriptName}`,
              method: "processAudience",
              data: { audience, stats }
            });
          }
        },
        { batchSize: 10 }
      );

    logger.info(`Completed processing audience: ${audience}`, {
      module: `script:${this.scriptName}`,
      method: "processAudience",
      data: { audience, finalStats: stats }
    });
  }
}

// Run the script
new SyncMailchimpUnsubscribersScript().run();
