import ScriptRunner from "../jobs/services/scriptRunner";
import logger from "../external-services/loggerService";
import { ChargeTransaction } from "../models/Transaction";

class RetryRejectedChargesQueryScriptRunner extends ScriptRunner {
  scriptName = "retry-rejected-charges-query";

  async processFn(): Promise<void> {
    logger.info("Retrying rejected charges...", {
      module: `script:${this.scriptName}`
    });

    const rejectedCharges = await ChargeTransaction.updateMany(
      {
        status: "Pending",
        category: "ChargeTransaction",
        "consideration.currency": "EUR",
        "providers.wealthkernel.status": "Rejected"
      },
      {
        $unset: { providers: 1 }
      }
    );

    logger.info(`Updated ${rejectedCharges.modifiedCount} charges`, {
      module: `script:${this.scriptName}`
    });

    logger.info("Finished retrying rejected charges!", {
      module: `script:${this.scriptName}`
    });
  }
}

new RetryRejectedChargesQueryScriptRunner().run();
