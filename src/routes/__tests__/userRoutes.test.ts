import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import { KycStatusEnum, PlatformType, User, UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAccount,
  buildAddress,
  buildAssetTransaction,
  buildBankAccount,
  buildDailyPortfolioSavingsTicker,
  buildDailyPortfolioTicker,
  buildDailySummarySnapshot,
  buildDepositCashTransaction,
  buildGift,
  buildHoldingDTO,
  buildIndexPrice,
  buildIntraDayPortfolioTicker,
  buildKycOperation,
  buildMandate,
  buildNotificationSettings,
  buildOrder,
  buildParticipant,
  buildPortfolio,
  buildReferralCode,
  buildReward,
  buildSavingsProduct,
  buildSavingsTopup,
  buildSubscription,
  buildSundownDigest,
  buildTopUpAutomation,
  buildUser,
  buildWallet,
  buildWealthyhoodDividendTransaction
} from "../../tests/utils/generateModels";
import { faker } from "@faker-js/faker";
import {
  AccountType,
  AddressType,
  CurrencyEnum,
  DocumentCodesType,
  PortfolioType,
  WealthkernelService
} from "../../external-services/wealthkernelService";
import {
  countriesConfig,
  entitiesConfig,
  indexesConfig,
  investmentUniverseConfig,
  whitelistConfig
} from "@wealthyhood/shared-configs";
import {
  buildWealthkernelAccountResponse,
  buildWealthkernelAddressResponse,
  buildWealthkernelPartyResponse,
  buildWealthkernelPortfoliosResponse
} from "../../tests/utils/generateWealthkernel";
import { Account, AccountDocument } from "../../models/Account";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import eventEmitter from "../../loaders/eventEmitter";
import events from "../../event-handlers/events";
import { Address, AddressDocument } from "../../models/Address";
import { TruelayerPaymentsClient } from "../../external-services/truelayerService";
import { buildProviderType } from "../../tests/utils/generateTruelayer";
import { BankAccountDocument } from "../../models/BankAccount";
import * as environmentUtil from "../../utils/environmentUtil";
import { LifetimeEnum, ReferralCode, ReferralCodeDocument } from "../../models/ReferralCode";
import { Reward, RewardDocument, RewardInvestmentActivityFilterEnum } from "../../models/Reward";
import { ProviderEnum } from "../../configs/providersConfig";
import { Gift } from "../../models/Gift";
import DateUtil from "../../utils/dateUtil";
import {
  AssetTransactionDocument,
  DepositCashTransaction,
  TransactionInvestmentActivityFilterEnum
} from "../../models/Transaction";
import { DepositMethodEnum } from "../../types/transactions";
import { StripeService } from "../../external-services/stripeService";
import { KycOperation, KycOperationDocument } from "../../models/KycOperation";
import { GoCardlessPaymentsService } from "../../external-services/goCardlessPaymentsService";
import { Wallet, WalletDocument } from "../../models/Wallet";
import { AccountStatusType, DevengoService, IdentifierType } from "../../external-services/devengoService";
import { SaltedgeService } from "../../external-services/saltedgeService";
import { UNKNOWN_LOGO_URL } from "../../utils/banksUtil";
import CloudflareService from "../../external-services/cloudflareService";
import { EmailNotificationSettingEnum, NotificationSettings } from "../../models/NotificationSettings";
import { SumsubService } from "../../external-services/sumsubService";
import { buildApplicant } from "../../tests/utils/generateSumsub";
import {
  IndividualSentimentScoreComponentEnum,
  TotalSentimentScoreComponentEnum
} from "../../models/DailySummarySnapshot";
import { CHART_LABEL_ONLY_POINTS_TO_INCLUDE } from "../../services/userService";
import { RedisClientService } from "../../loaders/redis";
import Decimal from "decimal.js/decimal";
import { PartialRecord } from "../../types/utils";
import { DailySummaryWithDataType } from "../../types/summaries";
import { DateTime } from "luxon";
import { MARKET_TRADING_HOURS } from "@wealthyhood/shared-configs/dist/marketHours";
import { OrderSubmissionIntentEnum } from "../../models/Order";

const { ASSET_CONFIG } = investmentUniverseConfig;

describe("UserRoutes", () => {
  /**
   * ====================
   * HELPER METHODS
   * ====================
   */

  const _startup = () => {
    jest.resetAllMocks();

    const TODAY = new Date("2024-01-10");
    Date.now = jest.fn(() => TODAY.valueOf());
  };
  const _teardown = async () => {
    await clearDb();
  };
  const _mockSuccessfulSumsubPassportVerification = async (user: UserDocument): Promise<void> => {
    await Promise.all([
      user.updateOne({
        providers: {
          ...user.providers,
          sumsub: {
            id: faker.string.uuid()
          }
        }
      }),
      buildKycOperation({
        status: "Passed",
        owner: user.id,
        activeProviders: [ProviderEnum.SUMSUB],
        providers: {
          sumsub: {
            id: faker.string.uuid(),
            status: "completed",
            decision: "GREEN"
          }
        }
      })
    ]);

    jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
      buildApplicant({
        info: {
          firstName: user.firstName,
          lastName: user.lastName,
          dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
          nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]]
        }
      })
    );
  };

  beforeAll(async () => await connectDb("UserRoutes"));
  afterAll(async () => await closeDb());

  /**
   * ====================
   * TEST GET REQUESTS
   * ====================
   */

  describe("GET /users/me/daily-summaries", () => {
    describe("when the user has not passed KYC", () => {
      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        user = await buildUser({ kycStatus: KycStatusEnum.FAILED });

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not passed kyc"
            }
          })
        );
      });
    });

    describe("when the user has no snapshots and no cash/savings/holdings", () => {
      const TODAY = new Date("2024-01-10T12:00:00+00:00");

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "notStarted" });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: []
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(TODAY, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          buildSundownDigest({
            date: TODAY,
            content: "Today's market summary..."
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data for today with isUninvested flag", async () => {
        expect(JSON.parse(response.text)).toMatchObject({
          maxValueDifferences: {
            cash: 100,
            holdings: 100,
            savings: 100,
            total: 100
          },
          data: expect.arrayContaining([
            expect.objectContaining({
              isUninvested: true,
              timestamp: TODAY.getTime(),
              shortDateLabel: "Today",
              fullDateLabel: "Today",
              chartLabel: "Wed\n10",
              portfolio: {
                cash: { key: "cash", value: 0, chartValue: 0, displayValue: "£0.00" },
                savings: { key: "savings", value: 0, chartValue: 0, displayValue: "£0.00" },
                holdings: { key: "holdings", value: 0, chartValue: 0, displayValue: "£0.00" },
                total: { key: "total", value: 0, chartValue: 0, displayValue: "£0.00" }
              },
              todayMarkets: expect.arrayContaining([
                {
                  label: "🇺🇸 S&P 500",
                  returns: { upBy: "22.00%" }
                }
              ])
            })
          ])
        });
      });

      it("should return no chart only label data points", async () => {
        expect(JSON.parse(response.text).data).toHaveLength(1);
      });
    });

    describe("when the user has no snapshots and no cash/savings/holdings but has an investment in progress", () => {
      const TODAY = new Date("2024-01-10T12:00:00+00:00");

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "inProgress" });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: []
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(TODAY, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          buildSundownDigest({
            date: TODAY,
            content: "Today's market summary..."
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should include portfolio data with no cash/savings/holdings and without isUninvested flag", async () => {
        expect(JSON.parse(response.text)).toMatchObject({
          maxValueDifferences: {
            cash: 100,
            holdings: 100,
            savings: 100,
            total: 100
          },
          data: expect.arrayContaining([
            expect.objectContaining({
              timestamp: TODAY.getTime(),
              shortDateLabel: "Today",
              fullDateLabel: "Today",
              chartLabel: "Wed\n10",
              portfolio: {
                cash: { key: "cash", value: 0, chartValue: 0, displayValue: "£0.00" },
                savings: { key: "savings", value: 0, chartValue: 0, displayValue: "£0.00" },
                holdings: { key: "holdings", value: 0, chartValue: 0, displayValue: "£0.00" },
                total: { key: "total", value: 0, chartValue: 0, displayValue: "£0.00" }
              },
              sundownDigest: "Today's market summary...",
              todayMarkets: expect.arrayContaining([{ label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }])
            })
          ])
        });

        expect(
          JSON.parse(response.text).data.find((point: any) => point.timestamp === TODAY.getTime()).isUninvested
        ).toBeUndefined();
      });

      it("should return chart only label data points", async () => {
        expect(JSON.parse(response.text).data).toHaveLength(7); // 6 chart only label points + 1 today
      });
    });

    describe("when the user has no snapshots and has cash", () => {
      const TODAY = new Date("2024-01-10T12:00:00+00:00");

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "notStarted" });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 500, reserved: 0, settled: 0 } },
          holdings: []
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(TODAY, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          buildSundownDigest({
            date: TODAY,
            content: "Today's market summary..."
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should include portfolio data with cash but without isUninvested flag", async () => {
        expect(JSON.parse(response.text)).toMatchObject({
          maxValueDifferences: {
            cash: 100,
            holdings: 100,
            savings: 100,
            total: 100
          },
          data: expect.arrayContaining([
            expect.objectContaining({
              timestamp: TODAY.getTime(),
              shortDateLabel: "Today",
              fullDateLabel: "Today",
              chartLabel: "Wed\n10",
              portfolio: {
                cash: { key: "cash", value: 500, chartValue: 500, displayValue: "£500.00" },
                savings: { key: "savings", value: 0, chartValue: 0, displayValue: "£0.00" },
                holdings: { key: "holdings", value: 0, chartValue: 0, displayValue: "£0.00" },
                total: { key: "total", value: 500, chartValue: 500, displayValue: "£500.00" }
              },
              sundownDigest: "Today's market summary...",
              todayMarkets: expect.arrayContaining([{ label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }])
            })
          ])
        });

        expect(
          JSON.parse(response.text).data.find((point: any) => point.timestamp === TODAY.getTime()).isUninvested
        ).toBeUndefined();
      });

      it("should return chart only label data points", async () => {
        expect(JSON.parse(response.text).data).toHaveLength(7); // 6 chart only label points + 1 today
      });
    });

    describe("when the user has no snapshots and has a pending savings top-up", () => {
      const TODAY = new Date("2024-01-10T12:00:00+00:00");

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => TODAY.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "notStarted" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          holdings: []
        });

        await buildSavingsTopup({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Pending",
          consideration: {
            amount: 10000, // EUR 100
            currency: "GBP"
          },
          savingsProduct: "mmf_dist_gbp"
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(TODAY, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          buildSundownDigest({
            date: TODAY,
            content: "Today's market summary..."
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should include portfolio data with cash but without isUninvested flag", async () => {
        expect(JSON.parse(response.text)).toMatchObject({
          maxValueDifferences: {
            cash: 100,
            holdings: 100,
            savings: 100,
            total: 100
          },
          data: expect.arrayContaining([
            expect.objectContaining({
              timestamp: TODAY.getTime(),
              shortDateLabel: "Today",
              fullDateLabel: "Today",
              chartLabel: "Wed\n10",
              portfolio: {
                cash: { key: "cash", value: 0, chartValue: 0, displayValue: "£0.00" },
                savings: { key: "savings", value: 100, chartValue: 100, displayValue: "£100.00" },
                holdings: { key: "holdings", value: 0, chartValue: 0, displayValue: "£0.00" },
                total: { key: "total", value: 100, chartValue: 100, displayValue: "£100.00" }
              },
              sundownDigest: "Today's market summary...",
              todayMarkets: expect.arrayContaining([{ label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }])
            })
          ])
        });

        expect(
          JSON.parse(response.text).data.find((point: any) => point.timestamp === TODAY.getTime()).isUninvested
        ).toBeUndefined();
      });
    });

    describe("when the user has backfilled data", () => {
      const NOW = new Date("2024-01-10T12:00:00+00:00");
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, { price: 1000 }, { dailyReturnPercentage: 0.0512 })
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  isBackfilled: true,
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          dailyReturnPercentage: 0.42
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return no up-by/down-by for portfolio holdings", async () => {
        const backfilledSummary = JSON.parse(response.text)?.data?.find(
          (summary: DailySummaryWithDataType) => summary.timestamp === 1704110400000
        );

        expect((backfilledSummary?.portfolio?.holdings as any)?.upBy).toBeUndefined();
        expect((backfilledSummary?.portfolio?.holdings as any)?.downBy).toBeUndefined();
      });

      it("should return backfilled data with disabled see all top performers", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1704110400000,
                fullDateLabel: "Monday, 01 Jan 2024",
                shortDateLabel: "Mon, 01 Jan",
                chartLabel: "Mon\n01",
                seeAllTopPerformersEnabled: false
              })
            ])
          })
        );
      });
    });

    describe("when the user is uninvested but has cash", () => {
      const NOW = new Date("2025-03-03T12:00:00+00:00"); // Monday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          portfolioConversionStatus: "notStarted"
        });
        await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: []
        });
        await Promise.all([
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 0, currency: "GBP" },
                      dailyUpBy: 0,
                      dailyReturnPercentage: 0,
                      assets: []
                    },
                    savings: {
                      value: { amount: 0, currency: "GBP" },
                      unrealisedInterest: { amount: 0, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 1000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data point for today", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1741003200000,
                chartLabel: "Mon\n03",
                fullDateLabel: "Today",
                shortDateLabel: "Today",
                portfolio: {
                  cash: { key: "cash", value: 10, chartValue: 10, displayValue: "£10.00" },
                  savings: {
                    key: "savings",
                    value: 0,
                    chartValue: 0,
                    displayValue: "£0.00",
                    estimated: true
                  },
                  total: { key: "total", value: 10, chartValue: 10, displayValue: "£10.00" },
                  holdings: {
                    key: "holdings",
                    value: 0,
                    chartValue: 0,
                    displayValue: "£0.00"
                  }
                },
                todayMarkets: expect.arrayContaining([
                  { label: "🇫🇷 CAC 40", returns: { upBy: "0.00%" } },
                  { label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }
                ])
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested", () => {
      const NOW = new Date("2025-03-03T12:00:00+00:00"); // Monday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };
      const EXPECTED_CURRENT_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.5,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.5,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.5,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.5
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 500 },
              { dailyReturnPercentage: 0.0512, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 500 },
              { dailyReturnPercentage: -0.0512, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildDailyPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            date: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSubscription({ owner: user.id })
        ]);

        const savingsProduct = await buildSavingsProduct(
          true,
          { commonId: "mmf_dist_gbp" },
          { date: DateUtil.getDateOfDaysAgo(NOW, 2), dailyDistributionFactor: 0.002 }
        );
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateNWorkDaysAgo(NOW, 1)
        });
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateOfDaysAgo(NOW, 1) // we should expect to have daily portfolio savings ticker for Sunday as well
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" },
                      unrealisedInterest: { amount: 1, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data from oldest -> newest", async () => {
        // Ensure we sort older -> newer
        expect(JSON.parse(response.text).data[CHART_LABEL_ONLY_POINTS_TO_INCLUDE]).toMatchObject({
          fullDateLabel: "Monday, 24 Feb 2025"
        });
      });

      it("should include chart label only data points", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Wed\n19"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Thu\n20"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Fri\n21"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Tue\n04"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Wed\n05"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Thu\n06"
              })
            ])
          })
        );
      });

      it("should return data points for all historical data", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1740398400000,
                fullDateLabel: "Monday, 24 Feb 2025",
                shortDateLabel: "Mon, 24 Feb",
                chartLabel: "Mon\n24",
                portfolio: {
                  cash: { key: "cash", value: 1000, chartValue: 1000, displayValue: "£1,000.00" },
                  savings: {
                    key: "savings",
                    value: 7000,
                    chartValue: 7000,
                    displayValue: "£7,000.00"
                  },
                  total: { key: "total", value: 11000, chartValue: 11000, displayValue: "£11,000.00" },
                  holdings: {
                    key: "holdings",
                    value: 3000,
                    chartValue: 3000,
                    displayValue: "£3,000.00",
                    upBy: "£10.00 · 5.00%"
                  }
                },
                sundownDigest: SUNDOWN_DIGEST_TEXT,
                sentimentScore: {
                  analyst: {
                    score: Decimal.mul(
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      100
                    ).toNumber(),
                    label: "optimal"
                  },
                  news: {
                    score: Decimal.mul(
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      100
                    ).toNumber(),
                    label: "optimal"
                  },
                  priceMomentum: {
                    score: Decimal.mul(
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM],
                      100
                    ).toNumber(),
                    label: "optimal"
                  },
                  total: {
                    score: Decimal.mul(
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      100
                    ).toNumber(),
                    label: "optimal"
                  }
                },
                todayMarkets: [{ label: "🇺🇸 S&P 500", returns: { upBy: "1.00%" } }],
                performers: {
                  all: [
                    {
                      assetId: "equities_apple",
                      value: "£3,000.00",
                      weight: "50.00%",
                      upBy: "42.00%"
                    },
                    {
                      assetId: "equities_nvidia",
                      value: "£3,000.00",
                      weight: "50.00%",
                      downBy: "40.00%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_apple",
                      value: "£3,000.00",
                      weight: "50.00%",
                      upBy: "42.00%"
                    }
                  ],
                  worst: [
                    {
                      assetId: "equities_nvidia",
                      value: "£3,000.00",
                      weight: "50.00%",
                      downBy: "40.00%"
                    }
                  ]
                }
              }),
              expect.objectContaining({
                fullDateLabel: "Friday, 28 Feb 2025",
                chartLabel: "Fri\n28"
              })
            ])
          })
        );
      });

      it("should include savings details only for data points after 28th Feb 2025", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                fullDateLabel: "Friday, 28 Feb 2025",
                shortDateLabel: "Fri, 28 Feb",
                chartLabel: "Fri\n28",
                portfolio: expect.objectContaining({
                  savings: {
                    key: "savings",
                    value: 7000,
                    chartValue: 7000,
                    displayValue: "£7,000.00",
                    dailyInterest: "+£0.01 / day",
                    unrealisedMonthlyInterest: "£1.00"
                  }
                })
              })
            ])
          })
        );
      });

      it("should return data point for today", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1741003200000,
                chartLabel: "Mon\n03",
                fullDateLabel: "Today",
                shortDateLabel: "Today",
                portfolio: {
                  cash: { key: "cash", value: 10, chartValue: 10, displayValue: "£10.00" },
                  savings: {
                    key: "savings",
                    value: 10,
                    chartValue: 10,
                    displayValue: "£10.00",
                    estimated: true,
                    dailyInterest: "+£0.02 / day",
                    unrealisedMonthlyInterest: "£0.04"
                  },
                  total: { key: "total", value: 1020, chartValue: 1020, displayValue: "£1,020.00" },
                  holdings: {
                    key: "holdings",
                    value: 1000,
                    chartValue: 1000,
                    displayValue: "£1,000.00",
                    upBy: "£200.00 · 25.00%"
                  }
                },
                sentimentScore: {
                  analyst: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  news: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  priceMomentum: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  total: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  }
                },
                todayMarkets: expect.arrayContaining([
                  { label: "🇫🇷 CAC 40", returns: { upBy: "0.00%" } },
                  { label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }
                ]),
                performers: {
                  all: [
                    {
                      assetId: "equities_apple",
                      value: "£500.00",
                      weight: "50.00%",
                      upBy: "5.12%"
                    },
                    {
                      assetId: "equities_microsoft",
                      value: "£500.00",
                      weight: "50.00%",
                      downBy: "5.12%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_apple",
                      value: "£500.00",
                      weight: "50.00%",
                      upBy: "5.12%"
                    }
                  ],
                  worst: [
                    {
                      assetId: "equities_microsoft",
                      value: "£500.00",
                      weight: "50.00%",
                      downBy: "5.12%"
                    }
                  ]
                }
              })
            ])
          })
        );
      });

      it("should return max value differences for each chart", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            maxValueDifferences: {
              cash: 990,
              holdings: 2000,
              savings: 6990,
              total: 9980
            }
          })
        );
      });
    });

    describe("when the user is invested and tomorrow is a UK bank holiday", () => {
      const NOW = new Date("2025-04-17T12:00:00+00:00"); // Thursday and tomorrow is Good Friday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.0512, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.0512, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            timestamp: DateUtil.getStartOfDay(NOW)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSubscription({ owner: user.id })
        ]);

        const savingsProduct = await buildSavingsProduct(
          true,
          { commonId: "mmf_dist_gbp" },
          { date: DateUtil.getDateOfDaysAgo(NOW, 1), dailyDistributionFactor: 0.002 }
        );
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateNWorkDaysAgo(NOW, 1)
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" },
                      unrealisedInterest: { amount: 1, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should include three chart label only data points before and three after the summary data points", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Thu\n03"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Fri\n04"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Mon\n07"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Fri\n18"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Mon\n21"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Tue\n22"
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and they have up-by but no returns", () => {
      const NOW = new Date("2025-02-26T12:00:00+00:00");
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.0512, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.0512, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            timestamp: DateUtil.getStartOfDay(NOW)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSubscription({ owner: user.id })
        ]);

        const savingsProduct = await buildSavingsProduct(
          true,
          { commonId: "mmf_dist_gbp" },
          { date: DateUtil.getDateOfDaysAgo(NOW, 1), dailyDistributionFactor: 0.002 }
        );
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" },
                      unrealisedInterest: { amount: 1, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only formatted up-by in portfolio holdings data points", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                fullDateLabel: "Monday, 17 Feb 2025",
                portfolio: expect.objectContaining({
                  holdings: {
                    key: "holdings",
                    value: 3000,
                    chartValue: 3000,
                    displayValue: "£3,000.00",
                    upBy: "£10.00"
                  }
                })
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and has portfolio value less than £1", () => {
      const NOW = new Date("2024-01-10T12:00:00+00:00");
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 0.45 },
              { dailyReturnPercentage: 0.0512, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 0.45 },
              { dailyReturnPercentage: -0.0512, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 0.5 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 0.8 },
            timestamp: DateUtil.getStartOfDay(NOW)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 0.9 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 0.5, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 0.75, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 0, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 1.25, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data points for all historical data", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1704110400000,
                fullDateLabel: "Monday, 01 Jan 2024",
                shortDateLabel: "Mon, 01 Jan",
                chartLabel: "Mon\n01",
                portfolio: {
                  cash: { key: "cash", value: 0.5, chartValue: 0.5, displayValue: "£0.50" },
                  savings: expect.objectContaining({
                    key: "savings",
                    value: 0,
                    chartValue: 0,
                    displayValue: "£0.00"
                  }),
                  total: { key: "total", value: 1.25, chartValue: 1.25, displayValue: "£1.25" },
                  holdings: expect.objectContaining({
                    key: "holdings",
                    value: 0.75,
                    chartValue: 0.75,
                    displayValue: "£0.75"
                  })
                }
              })
            ])
          })
        );
      });

      it("should return data point for today", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1704888000000,
                chartLabel: "Wed\n10",
                fullDateLabel: "Today",
                shortDateLabel: "Today",
                portfolio: expect.objectContaining({
                  holdings: expect.objectContaining({
                    key: "holdings",
                    value: 0.9,
                    chartValue: 0.9,
                    displayValue: "£0.90"
                  })
                })
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and US stock market is not open yet", () => {
      const NOW = new Date("2024-01-10T12:00:00+00:00");
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });

        // User holds both US stocks and one ETF but the US stocks have not been traded yet today.
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.0512, timestamp: DateUtil.getDateOfDaysAgo(NOW, 1) }
            ),
            await buildHoldingDTO(
              true,
              "equities_uk",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.05, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_us",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.01, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_jp",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.01, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            timestamp: DateUtil.getStartOfDay(NOW)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data point for today with US assets not having a return and at the bottom of all investments", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                fullDateLabel: "Today",
                performers: {
                  all: [
                    {
                      assetId: "equities_jp",
                      value: "£1,000.00",
                      weight: "25.00%",
                      upBy: "1.00%"
                    },
                    {
                      assetId: "equities_us",
                      value: "£1,000.00",
                      weight: "25.00%",
                      downBy: "1.00%"
                    },
                    {
                      assetId: "equities_uk",
                      value: "£1,000.00",
                      weight: "25.00%",
                      downBy: "5.00%"
                    },
                    {
                      assetId: "equities_apple",
                      value: "£1,000.00",
                      weight: "25.00%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_jp",
                      value: "£1,000.00",
                      weight: "25.00%",
                      upBy: "1.00%"
                    }
                  ],
                  worst: [
                    {
                      assetId: "equities_uk",
                      value: "£1,000.00",
                      weight: "25.00%",
                      downBy: "5.00%"
                    },
                    {
                      assetId: "equities_us",
                      value: "£1,000.00",
                      weight: "25.00%",
                      downBy: "1.00%"
                    }
                  ]
                }
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and there is a day with more than one index price", () => {
      const NOW = new Date("2024-01-10T12:00:00+00:00");
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, { price: 1000 }, { dailyReturnPercentage: 0.0512 }),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.0512 }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date: DateUtil.getDateOfHoursAgo(date, 1)
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.02,
                  date: DateUtil.getDateOfHoursAgo(date, 2)
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data points with latest index prices for all historical data", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: 1704110400000,
                fullDateLabel: "Monday, 01 Jan 2024",
                shortDateLabel: "Mon, 01 Jan",
                chartLabel: "Mon\n01",
                todayMarkets: [{ label: "🇺🇸 S&P 500", returns: { upBy: "1.00%" } }]
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and today is a weekend", () => {
      const NOW = new Date("2025-02-02T12:00:00+00:00"); // Today is Sunday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, { price: 1000 }, { dailyReturnPercentage: 0.0512 }),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.0512 }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should include chart label only data points", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Tue\n21"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Wed\n22"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Thu\n23"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Mon\n03"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Tue\n04"
              }),
              expect.objectContaining({
                isOnlyForChartLabel: true,
                chartLabel: "Wed\n05"
              })
            ])
          })
        );
      });

      it("should NOT return data point for today", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.not.arrayContaining([
              expect.objectContaining({
                shortDateLabel: "Today"
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and have multiple assets and the markets are open", () => {
      // 15:30 New York time - 20:30 London time
      const newYorkMarketTime = DateTime.utc(2024, 6, 26)
        .setZone(MARKET_TRADING_HOURS.US.timeZone)
        .set({
          hour: 15,
          minute: 30
        })
        .toJSDate()
        .valueOf();
      const NOW = new Date(newYorkMarketTime);

      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => +NOW);

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });

        // User holds both US stocks and one ETF and the US stocks have been traded today.
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.05, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.06, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_uk",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.05, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_us",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.01, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_jp",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.01, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_global_pharma",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.002, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_china",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.003, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_eu",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.004, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_global",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.003, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_global_clean_energy",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.004, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            timestamp: DateUtil.getStartOfDay(NOW)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: NOW
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data points for today with the correct investments performance order and only a maximum of 3 best performers and 3 worst performers", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                fullDateLabel: "Today",
                performers: {
                  all: [
                    {
                      assetId: "equities_apple",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "5.00%"
                    },
                    {
                      assetId: "equities_jp",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "1.00%"
                    },
                    {
                      assetId: "equities_eu",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "0.40%"
                    },
                    {
                      assetId: "equities_china",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "0.30%"
                    },
                    {
                      assetId: "equities_global_pharma",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "0.20%"
                    },
                    {
                      assetId: "equities_global",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "0.30%"
                    },
                    {
                      assetId: "equities_global_clean_energy",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "0.40%"
                    },
                    {
                      assetId: "equities_us",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "1.00%"
                    },
                    {
                      assetId: "equities_uk",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "5.00%"
                    },
                    {
                      assetId: "equities_microsoft",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "6.00%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_apple",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "5.00%"
                    },
                    {
                      assetId: "equities_jp",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "1.00%"
                    },
                    {
                      assetId: "equities_eu",
                      value: "£1,000.00",
                      weight: "10.00%",
                      upBy: "0.40%"
                    }
                  ],
                  worst: [
                    {
                      assetId: "equities_microsoft",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "6.00%"
                    },
                    {
                      assetId: "equities_uk",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "5.00%"
                    },
                    {
                      assetId: "equities_us",
                      value: "£1,000.00",
                      weight: "10.00%",
                      downBy: "1.00%"
                    }
                  ]
                }
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested, but there have been no intraday portfolio tickers created for today yet", () => {
      const NOW = new Date("2025-02-04T04:00:00+00:00"); // Today is Tuesday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(true, "equities_apple", 1, { price: 1000 }, { dailyReturnPercentage: 0.0512 }),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: -0.0512 }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 2)
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return no up-by/down-by for portfolio holdings", async () => {
        const todaySummary = JSON.parse(response.text)?.data?.find(
          (summary: DailySummaryWithDataType) => summary.timestamp === NOW.getTime()
        );

        expect((todaySummary?.portfolio?.holdings as any)?.upBy).toBeUndefined();
        expect((todaySummary?.portfolio?.holdings as any)?.downBy).toBeUndefined();
      });
    });

    describe("when the user is invested, the markets are open, but there are holdings with ZERO returns", () => {
      const NOW = new Date("2025-02-04T04:00:00+00:00"); // Today is Tuesday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0, timestamp: NOW }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 1000 },
              { dailyReturnPercentage: 0.05, timestamp: NOW }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 500 },
            timestamp: NOW
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 2)
          }),
          buildSavingsProduct(true, { commonId: "mmf_dist_gbp" }, { date: DateUtil.getDateOfDaysAgo(NOW, 1) }),
          buildSubscription({ owner: user.id })
        ]);

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: DateUtil.getDateOfHoursAgo(NOW, 1).valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                buildDailySummarySnapshot({
                  metadata: {
                    owner: user.id
                  },
                  sentimentScore: {
                    [TotalSentimentScoreComponentEnum.TOTAL]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                    [IndividualSentimentScoreComponentEnum.NEWS]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                    [IndividualSentimentScoreComponentEnum.ANALYST]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                    [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                      PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                  },
                  portfolio: {
                    cash: {
                      value: { amount: 1000, currency: "GBP" }
                    },
                    holdings: {
                      value: { amount: 3000, currency: "GBP" },
                      dailyUpBy: 10,
                      dailyReturnPercentage: 0.05,
                      assets: [
                        {
                          assetId: "equities_apple",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: 0.42
                        },
                        {
                          assetId: "equities_nvidia",
                          quantity: 1,
                          latestPrice: { amount: 3000, currency: "GBP" },
                          holdingWeightPercentage: 0.5,
                          dailyReturnPercentage: -0.4
                        }
                      ]
                    },
                    savings: {
                      value: { amount: 7000, currency: "GBP" }
                    },
                    total: {
                      value: { amount: 11000, currency: "GBP" }
                    }
                  },
                  date
                }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data points for today with zero holding returning an upby of 0, but not show in best performers section", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                fullDateLabel: "Today",
                performers: {
                  all: [
                    {
                      assetId: "equities_microsoft",
                      value: "£1,000.00",
                      weight: "50.00%",
                      upBy: "5.00%"
                    },
                    {
                      assetId: "equities_apple",
                      value: "£1,000.00",
                      weight: "50.00%",
                      upBy: "0.00%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_microsoft",
                      value: "£1,000.00",
                      weight: "50.00%",
                      upBy: "5.00%"
                    }
                  ],
                  worst: []
                }
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested, but there is no daily snapshot for yesterday yet", () => {
      const NOW = new Date("2025-03-05T12:00:00+00:00"); // Wednesday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };
      const EXPECTED_CURRENT_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.5,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.5,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.5,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.5
      };

      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 500 },
              { dailyReturnPercentage: 0.0512, timestamp: DateUtil.getDateOfDaysAgo(NOW, 1) }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 500 },
              { dailyReturnPercentage: -0.0512, timestamp: DateUtil.getDateOfDaysAgo(NOW, 1) }
            )
          ]
        });
        await Promise.all([
          buildDailyPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            date: new Date("2025-03-03T08:00:00+00:00") // 2 days ago at 8 am
          }),
          buildDailyPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            date: new Date("2025-03-04T08:00:00+00:00") // 1 day ago at 8 am
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: new Date("2025-03-04T12:00:00+00:00") // 1 day ago at 12 pm
          }),
          buildSubscription({ owner: user.id })
        ]);

        const savingsProduct = await buildSavingsProduct(
          true,
          { commonId: "mmf_dist_gbp" },
          { date: DateUtil.getDateOfDaysAgo(NOW, 2), dailyDistributionFactor: 0.002 }
        );
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: NOW.valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);
            // skip daily snapshot for today & yesterday but create sundown digest and
            // index prices to use in yesterday's ticker
            const skipSnapshot = daysAgo === 0 || daysAgo === 1;

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                !skipSnapshot &&
                  buildDailySummarySnapshot({
                    metadata: {
                      owner: user.id
                    },
                    sentimentScore: {
                      [TotalSentimentScoreComponentEnum.TOTAL]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      [IndividualSentimentScoreComponentEnum.NEWS]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      [IndividualSentimentScoreComponentEnum.ANALYST]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                    },
                    portfolio: {
                      cash: {
                        value: { amount: 1000, currency: "GBP" }
                      },
                      holdings: {
                        value: { amount: 3000, currency: "GBP" },
                        dailyUpBy: 10,
                        dailyReturnPercentage: 0.05,
                        assets: [
                          {
                            assetId: "equities_apple",
                            quantity: 1,
                            latestPrice: { amount: 3000, currency: "GBP" },
                            holdingWeightPercentage: 0.5,
                            dailyReturnPercentage: 0.42
                          },
                          {
                            assetId: "equities_nvidia",
                            quantity: 1,
                            latestPrice: { amount: 3000, currency: "GBP" },
                            holdingWeightPercentage: 0.5,
                            dailyReturnPercentage: -0.4
                          }
                        ]
                      },
                      savings: {
                        value: { amount: 7000, currency: "GBP" },
                        unrealisedInterest: { amount: 1, currency: "GBP" }
                      },
                      total: {
                        value: { amount: 11000, currency: "GBP" }
                      }
                    },
                    date
                  }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return data point for yesterday", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: DateUtil.getDateOfDaysAgo(NOW, 1).getTime(),
                chartLabel: "Tue\n04",
                fullDateLabel: "Yesterday",
                shortDateLabel: "Yesterday",
                portfolio: {
                  cash: { key: "cash", value: 10, chartValue: 10, displayValue: "£10.00" },
                  savings: {
                    key: "savings",
                    value: 10,
                    chartValue: 10,
                    displayValue: "£10.00",
                    estimated: true,
                    dailyInterest: "+£0.02 / day",
                    unrealisedMonthlyInterest: "£0.01"
                  },
                  total: { key: "total", value: 1020, chartValue: 1020, displayValue: "£1,020.00" },
                  holdings: {
                    key: "holdings",
                    value: 1000,
                    chartValue: 1000,
                    displayValue: "£1,000.00",
                    upBy: "£200.00 · 25.00%"
                  }
                },
                sundownDigest: SUNDOWN_DIGEST_TEXT,
                sentimentScore: {
                  analyst: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  news: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  priceMomentum: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  total: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  }
                },
                todayMarkets: expect.arrayContaining([{ label: "🇺🇸 S&P 500", returns: { upBy: "1.00%" } }]),
                performers: {
                  all: [
                    {
                      assetId: "equities_apple",
                      value: "£500.00",
                      weight: "50.00%",
                      upBy: "5.12%"
                    },
                    {
                      assetId: "equities_microsoft",
                      value: "£500.00",
                      weight: "50.00%",
                      downBy: "5.12%"
                    }
                  ],
                  best: [
                    {
                      assetId: "equities_apple",
                      value: "£500.00",
                      weight: "50.00%",
                      upBy: "5.12%"
                    }
                  ],
                  worst: [
                    {
                      assetId: "equities_microsoft",
                      value: "£500.00",
                      weight: "50.00%",
                      downBy: "5.12%"
                    }
                  ]
                }
              })
            ])
          })
        );
      });

      it("should return data point for today", async () => {
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            data: expect.arrayContaining([
              expect.objectContaining({
                timestamp: NOW.getTime(),
                chartLabel: "Wed\n05",
                fullDateLabel: "Today",
                shortDateLabel: "Today",
                portfolio: {
                  cash: { key: "cash", value: 10, chartValue: 10, displayValue: "£10.00" },
                  savings: {
                    key: "savings",
                    value: 10,
                    chartValue: 10,
                    displayValue: "£10.00",
                    estimated: true,
                    dailyInterest: "+£0.02 / day",
                    unrealisedMonthlyInterest: "£0.03"
                  },
                  total: { key: "total", value: 1020, chartValue: 1020, displayValue: "£1,020.00" },
                  holdings: {
                    key: "holdings",
                    value: 1000,
                    chartValue: 1000,
                    displayValue: "£1,000.00"
                  }
                },
                sentimentScore: {
                  analyst: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  news: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  priceMomentum: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  },
                  total: {
                    score: Decimal.mul(
                      EXPECTED_CURRENT_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      100
                    ).toNumber(),
                    label: "suboptimal"
                  }
                },
                todayMarkets: expect.arrayContaining([{ label: "🇺🇸 S&P 500", returns: { upBy: "22.00%" } }]),
                performers: {
                  all: [
                    {
                      assetId: "equities_apple",
                      value: "£500.00",
                      weight: "50.00%"
                    },
                    {
                      assetId: "equities_microsoft",
                      value: "£500.00",
                      weight: "50.00%"
                    }
                  ],
                  best: [],
                  worst: []
                }
              })
            ])
          })
        );
      });
    });

    describe("when the user is invested and there IS a daily snapshot for yesterday", () => {
      const NOW = new Date("2025-03-05T12:00:00+00:00"); // Wednesday
      const SUNDOWN_DIGEST_TEXT =
        "As the sun sets on another bustling day in the world of finance, let\u2019s unpack the notable moves that shaped the markets today. In a surprising turn of events, Nordstrom (JWN) exceeded Wall Street\u2019s expectations.";

      const PORTFOLIO_HISTORICAL_SENTIMENT_SCORES = {
        [IndividualSentimentScoreComponentEnum.ANALYST]: 0.8,
        [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]: 0.8,
        [IndividualSentimentScoreComponentEnum.NEWS]: 0.8,
        [TotalSentimentScoreComponentEnum.TOTAL]: 0.8
      };
      let user: UserDocument;
      let response: request.Response;

      beforeAll(async () => {
        jest.clearAllMocks();
        Date.now = jest.fn(() => NOW.valueOf());

        user = await buildUser({ kycStatus: KycStatusEnum.PASSED, portfolioConversionStatus: "completed" });
        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
          holdings: [
            await buildHoldingDTO(
              true,
              "equities_apple",
              1,
              { price: 500 },
              { dailyReturnPercentage: 0.0512, timestamp: DateUtil.getDateOfDaysAgo(NOW, 1) }
            ),
            await buildHoldingDTO(
              true,
              "equities_microsoft",
              1,
              { price: 500 },
              { dailyReturnPercentage: -0.0512, timestamp: DateUtil.getDateOfDaysAgo(NOW, 1) }
            )
          ]
        });
        await Promise.all([
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 800 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildIntraDayPortfolioTicker({
            portfolio: portfolio._id,
            pricePerCurrency: { GBP: 1000 },
            timestamp: DateUtil.getDateOfDaysAgo(NOW, 1)
          }),
          buildSubscription({ owner: user.id })
        ]);

        const savingsProduct = await buildSavingsProduct(
          true,
          { commonId: "mmf_dist_gbp" },
          { date: DateUtil.getDateOfDaysAgo(NOW, 2), dailyDistributionFactor: 0.002 }
        );
        await buildDailyPortfolioSavingsTicker({
          portfolio: portfolio._id,
          savingsProduct: savingsProduct._id,
          dailyAccrual: 1, // in cents
          date: DateUtil.getDateOfDaysAgo(NOW, 1)
        });

        const cachedIndexPrices: PartialRecord<
          indexesConfig.IndexType,
          { close: number; timestamp: number; dailyReturnPercentage: number }
        > = {
          sp500: {
            close: 1000,
            timestamp: NOW.valueOf(),
            dailyReturnPercentage: 0.22
          }
        };

        await Promise.all([
          RedisClientService.Instance.set("fmp:today:latest_index_prices", cachedIndexPrices),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_apple:news", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:analyst", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:price_momentum", 0.5),
          RedisClientService.Instance.set("sentiment_scores:equities_microsoft:news", 0.5),
          // [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]
          ...[...Array(10).keys()].map(async (daysAgo) => {
            const date = DateUtil.getDateOfDaysAgo(NOW, daysAgo);
            // skip daily snapshot for today
            const skipSnapshot = daysAgo === 0;

            if (!DateUtil.isWeekend(date)) {
              await Promise.all([
                !skipSnapshot &&
                  buildDailySummarySnapshot({
                    metadata: {
                      owner: user.id
                    },
                    sentimentScore: {
                      [TotalSentimentScoreComponentEnum.TOTAL]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[TotalSentimentScoreComponentEnum.TOTAL],
                      [IndividualSentimentScoreComponentEnum.NEWS]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.NEWS],
                      [IndividualSentimentScoreComponentEnum.ANALYST]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.ANALYST],
                      [IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]:
                        PORTFOLIO_HISTORICAL_SENTIMENT_SCORES[IndividualSentimentScoreComponentEnum.PRICE_MOMENTUM]
                    },
                    portfolio: {
                      cash: {
                        value: { amount: 1000, currency: "GBP" }
                      },
                      holdings: {
                        value: { amount: 3000, currency: "GBP" },
                        dailyUpBy: 10,
                        dailyReturnPercentage: 0.05,
                        assets: [
                          {
                            assetId: "equities_apple",
                            quantity: 1,
                            latestPrice: { amount: 3000, currency: "GBP" },
                            holdingWeightPercentage: 0.5,
                            dailyReturnPercentage: 0.42
                          },
                          {
                            assetId: "equities_nvidia",
                            quantity: 1,
                            latestPrice: { amount: 3000, currency: "GBP" },
                            holdingWeightPercentage: 0.5,
                            dailyReturnPercentage: -0.4
                          }
                        ]
                      },
                      savings: {
                        value: { amount: 7000, currency: "GBP" },
                        unrealisedInterest: { amount: 1, currency: "GBP" }
                      },
                      total: {
                        value: { amount: 11000, currency: "GBP" }
                      }
                    },
                    date
                  }),
                buildSundownDigest({
                  date,
                  content: SUNDOWN_DIGEST_TEXT
                }),
                buildIndexPrice({
                  index: "sp500",
                  price: 1000,
                  dailyReturnPercentage: 0.01,
                  date
                })
              ]);
            }
          })
        ]);

        response = await request(app)
          .get("/api/m2m/users/me/daily-summaries")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return 200", async () => {
        expect(response.status).toEqual(200);
      });

      it("should return only 1 data point for yesterday", async () => {
        expect(JSON.parse(response.text).data.filter((x) => x.shortDateLabel === "Yesterday").length).toEqual(1);
      });
    });
  });

  describe("GET /users/:id/linked-bank-accounts", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should return status 200 with empty data when user does not have any bank accounts", async () => {
      const provider = buildProviderType({ provider_id: "mock-payments-gb-redirect" });
      jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([provider]);

      const user = await buildUser({}, false);
      const response = await request(app)
        .get(`/api/m2m/users/${user._id}/linked-bank-accounts`)
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data.length).toEqual(0);
    });

    it("should return status 200 with bank accounts array when user has European company entity", async () => {
      const user = await buildUser(
        {
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        },
        false
      );

      const bankAccount = await buildBankAccount({
        owner: user.id,
        number: null,
        sortCode: null,
        name: faker.finance.accountName(),
        iban: faker.finance.iban(),
        bankName: "Alphabank",
        activeProviders: [ProviderEnum.WEALTHKERNEL]
      });

      const response = await request(app)
        .get(`/api/m2m/users/${user._id}/linked-bank-accounts`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data[0]).toMatchObject(JSON.parse(JSON.stringify(bankAccount)));
    });

    it("should return status 200 with bank accounts array when user has UK company entity", async () => {
      const BANK_ID = "mock-payments-gb-redirect";

      const user = await buildUser({}, false);
      const bankAccount = await buildBankAccount({
        owner: user.id,
        providers: { truelayer: { bankId: BANK_ID } }
      });

      const response = await request(app)
        .get(`/api/m2m/users/${user._id}/linked-bank-accounts`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      const { id, name, number, sortCode, truelayerProviderId } = bankAccount;

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data[0]).toMatchObject({
        id,
        name,
        number,
        sortCode,
        truelayerProviderId,
        owner: user.id
      });
    });

    it("should return status 200 with bank accounts array (2 elements) when user has UK company entity", async () => {
      const BANK_ID = "mock-payments-gb-redirect";

      const user = await buildUser({}, false);

      const bankAccounts = await Promise.all([
        buildBankAccount({ owner: user.id, bankId: BANK_ID, providers: { truelayer: { bankId: BANK_ID } } }),
        buildBankAccount({ owner: user.id, bankId: BANK_ID, providers: { truelayer: { bankId: BANK_ID } } }),
        buildBankAccount({
          owner: user.id,
          bankId: BANK_ID,
          providers: { truelayer: { bankId: BANK_ID } },
          active: false
        })
      ]);

      const response = await request(app)
        .get(`/api/m2m/users/${user._id}/linked-bank-accounts`)
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const resBankAccounts = JSON.parse(response.text).data;
      expect(resBankAccounts.length).toEqual(2);
      bankAccounts
        .filter((ba) => ba.active)
        .forEach((bankAccount) => {
          expect(resBankAccounts.find((ba: BankAccountDocument) => ba.id == bankAccount.id)).toMatchObject({
            id: bankAccount.id,
            name: bankAccount.name,
            number: bankAccount.number,
            sortCode: bankAccount.sortCode,
            displayAccountIdentifier: `${bankAccount.sortCode} ${bankAccount.number}`,
            bankIconURL: UNKNOWN_LOGO_URL,
            displayBankName: "Mock UK Bank",
            truelayerProviderId: bankAccount.truelayerProviderId
          });
        });
    });
  });

  describe("GET /users/me/linked-bank-accounts", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should return status 200 with empty data when user does not have any bank accounts", async () => {
      const user = await buildUser({}, false);
      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user._id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data.length).toEqual(0);
    });

    it("should return status 200 with mandate populated if it exists on a bank account", async () => {
      const user = await buildUser({}, false);
      const bankAccount = await buildBankAccount({
        owner: user.id,
        providers: { truelayer: { bankId: "mock" } }
      });
      const provider = buildProviderType({ provider_id: bankAccount.providers?.truelayer?.bankId });
      const mandate = await buildMandate({
        bankAccount: bankAccount.id,
        category: "Top-Up",
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });

      jest.spyOn(TruelayerPaymentsClient.prototype, "getProviders").mockResolvedValue([provider]);

      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const resBankAccounts: BankAccountDocument[] = JSON.parse(response.text).data;
      expect(resBankAccounts.length).toEqual(1);
      expect(resBankAccounts[0]).toMatchObject(
        expect.objectContaining({
          id: bankAccount.id,
          name: bankAccount.name,
          number: bankAccount.number,
          sortCode: bankAccount.sortCode,
          mandate: expect.objectContaining({ _id: mandate.id, isActive: true })
        })
      );
    });

    it("should return status 200 with bank accounts array when user is based in European company entity", async () => {
      const user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE }, false);
      const [goCardlessBasedBankAccount, devengoBasedBankAccountWithoutBankId] = await Promise.all([
        buildBankAccount({
          owner: user.id,
          iban: faker.finance.iban(),
          bankId: "alphabank",
          bic: "CRBAGRAA",
          name: user.fullName,
          activeProviders: [ProviderEnum.GOCARDLESS_DATA],
          providers: {
            gocardlessData: {
              id: faker.string.uuid(),
              bankId: "ALPHABANK_CRBAGRAA"
            }
          }
        }),
        buildBankAccount({
          owner: user.id,
          iban: faker.finance.iban(),
          bic: "ERBKGRAAXXX",
          bankName: "Eurobank",
          name: user.fullName,
          activeProviders: [ProviderEnum.DEVENGO],
          providers: {
            devengo: {
              id: faker.string.uuid()
            }
          }
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            displayBankName: "Alpha Bank",
            bankIconURL: "https://img.wealthyhood.dev/bank-icons/AlphaBank.png",
            displayAccountIdentifier: goCardlessBasedBankAccount.iban,
            iban: goCardlessBasedBankAccount.iban,
            name: goCardlessBasedBankAccount.name,
            active: true,
            owner: user.id,
            activeProviders: [ProviderEnum.GOCARDLESS_DATA]
          }),
          expect.objectContaining({
            displayBankName: devengoBasedBankAccountWithoutBankId.bankName,
            bankIconURL: "https://img.wealthyhood.dev/bank-icons/EuroBank.png",
            displayAccountIdentifier: devengoBasedBankAccountWithoutBankId.iban,
            iban: devengoBasedBankAccountWithoutBankId.iban,
            name: devengoBasedBankAccountWithoutBankId.name,
            bic: devengoBasedBankAccountWithoutBankId.bic,
            active: true,
            owner: user.id,
            activeProviders: [ProviderEnum.DEVENGO]
          })
        ])
      );
    });

    it("should return status 200 with bank accounts with supportsBankTransfer set to false even if account has an available pay provider", async () => {
      const user = await buildUser(
        { companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE, residencyCountry: "GR" },
        false
      );

      await buildBankAccount({
        owner: user.id,
        iban: faker.finance.iban(),
        bic: "REVOGB2L",
        name: user.fullName,
        bankId: "revolut"
      });

      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            displayBankName: "Revolut",
            supportsEasyTransfer: false
          })
        ])
      );
    });

    it("should return status 200 with bank accounts array when user is based in UK company entity", async () => {
      const BANK_ID = "mock-payments-gb-redirect";

      const user = await buildUser({}, false);
      const bankAccount = await buildBankAccount({
        owner: user.id,
        providers: { truelayer: { bankId: BANK_ID } }
      });

      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      const { id, name, number, sortCode, truelayerProviderId } = bankAccount;

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text).data[0]).toMatchObject({
        id,
        name,
        number,
        sortCode,
        truelayerProviderId,
        owner: user.id
      });
    });

    it("should return status 200 with bank accounts array (2 elements) when user is based in UK company entity", async () => {
      const BANK_ID = "mock-payments-gb-redirect";

      const user = await buildUser({}, false);

      const bankAccounts = await Promise.all([
        buildBankAccount({
          owner: user.id,
          bankId: "mock-payments-gb-redirect",
          providers: { truelayer: { bankId: BANK_ID } }
        }),
        buildBankAccount({
          owner: user.id,
          bankId: "mock-payments-gb-redirect",
          providers: { truelayer: { bankId: BANK_ID } }
        }),
        buildBankAccount({
          owner: user.id,
          bankId: "mock-payments-gb-redirect",
          providers: { truelayer: { bankId: BANK_ID } },
          active: false
        })
      ]);

      const response = await request(app)
        .get("/api/m2m/users/me/linked-bank-accounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);

      const resBankAccounts = JSON.parse(response.text).data;
      expect(resBankAccounts.length).toEqual(2);
      bankAccounts
        .filter((ba) => ba.active)
        .forEach((bankAccount) => {
          expect(resBankAccounts.find((ba: BankAccountDocument) => ba.id == bankAccount.id)).toMatchObject({
            id: bankAccount.id,
            name: bankAccount.name,
            number: bankAccount.number,
            sortCode: bankAccount.sortCode,
            displayAccountIdentifier: `${bankAccount.sortCode} ${bankAccount.number}`,
            bankIconURL: UNKNOWN_LOGO_URL,
            displayBankName: "Mock UK Bank",
            truelayerProviderId: bankAccount.truelayerProviderId
          });
        });
    });
  });

  describe("GET /users/me", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should return the user that made the request", async () => {
      const user = await buildUser({}, false);
      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toMatchObject(JSON.parse(JSON.stringify(user)));
    });

    it("should return the user that made the request with populated fields if populate query param is passed", async () => {
      const user = await buildUser({});
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const receivedUser = JSON.parse(response.text);

      await user.populate("addresses bankAccounts");

      expect(receivedUser).toMatchObject(JSON.parse(JSON.stringify(user)));
    });

    it("should return the user that made the request with canUnlockFreeShare field set to false if populate is passed", async () => {
      const user = await buildUser({});
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts,canUnlockFreeShare")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          canUnlockFreeShare: false
        })
      );
    });

    it("should return the user that made the request with canUnlockFreeShare field set to true if populate is passed and user can unlock free share", async () => {
      const user = await buildUser({ createdAt: new Date(Date.now()), referredByEmail: faker.internet.email() });
      await buildAddress({ owner: user._id });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        initialHoldingsAllocation: [
          {
            percentage: 100,
            assetCommonId: "equities_us"
          }
        ]
      });
      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Settled",
        originalInvestmentAmount: 4000,
        consideration: {
          amount: 4000,
          currency: "GBP"
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts,canUnlockFreeShare")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser.canUnlockFreeShare).toBe(true);
    });

    it("should return the user that made the request with canReceiveCashback field set to false if populate is passed", async () => {
      const user = await buildUser({});
      await buildSubscription({ owner: user.id, price: "free_monthly" });
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts,canReceiveCashback")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          canReceiveCashback: false
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to completed if user has holdings & persisted portfolio conversion status completed", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "completed"
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: [await buildHoldingDTO(true, "equities_us", 1)]
      });
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "completed"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to fullyWithdrawn if user has no holdings & persisted portfolio conversion status completed", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "completed"
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "fullyWithdrawn"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to inProgress if user has persisted portfolio conversion status inProgress", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "inProgress"
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildAddress({ owner: user._id });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=addresses,bankAccounts")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "inProgress"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to inProgress if user has persisted portfolio conversion status notStarted and pending repeating investment", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      const [portfolio, automation] = await Promise.all([
        buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          holdings: []
        }),
        buildTopUpAutomation({
          owner: user.id
        }),
        buildSubscription({
          owner: user.id
        })
      ]);
      const deposit = await buildDepositCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending",
        depositMethod: DepositMethodEnum.DIRECT_DEBIT,
        linkedAutomation: automation.id
      });
      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "buy",
        pendingDeposit: deposit.id,
        status: "PendingDeposit",
        linkedAutomation: automation.id
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "inProgress"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to inProgress if user has persisted portfolio conversion status notStarted and pending portfolio buy with cash", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildSubscription({
        owner: user.id
      });
      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "buy",
        status: "Pending"
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "inProgress"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to inProgress if user has persisted portfolio conversion status notStarted and pending portfolio buy with bank where deposit flow is completed", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildSubscription({
        owner: user.id
      });
      const deposit = await buildDepositCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending",
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "authorized"
          }
        }
      });
      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "buy",
        status: "PendingDeposit",
        pendingDeposit: deposit.id
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "inProgress"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to notStarted if user has persisted portfolio conversion status notStarted and pending portfolio buy with bank where deposit flow is aborted", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildSubscription({
        owner: user.id
      });
      const deposit = await buildDepositCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending",
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            version: "v3",
            status: "authorization_required"
          }
        }
      });
      await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "buy",
        status: "PendingDeposit",
        pendingDeposit: deposit.id
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "notStarted"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to notStarted if user has persisted portfolio conversion status notStarted and pending deposit", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      const portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildSubscription({
        owner: user.id
      });
      await buildDepositCashTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        status: "Pending"
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "notStarted"
        })
      );
    });

    it("should return the user that made the request with portfolioConversionStatus field set to notStarted if user has persisted portfolio conversion status notStarted and no transactions", async () => {
      const user = await buildUser({
        portfolioConversionStatus: "notStarted"
      });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        holdings: []
      });
      await buildSubscription({
        owner: user.id
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          portfolioConversionStatus: "notStarted"
        })
      );
    });

    it("should return the user that made the request with isVerified field set to true if user is KYC passed and they have a WK portfolio", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Active"
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          isVerified: true
        })
      );
    });

    it("should return the user that made the request with isVerified field set to false if user is KYC passed and they don't have a WK portfolio", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {}
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          isVerified: false
        })
      );
    });

    it("should return the user that made the request without the isVerified field if user is KYC passed and portfolios are not populated", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Active"
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.not.objectContaining({
          isVerified: true
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to false if user is KYC failed", async () => {
      const user = await buildUser({ kycStatus: "failed" });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: false
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to false if user is KYC pending", async () => {
      const user = await buildUser({ kycStatus: "pending" });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: false
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to false if user is Verified but the user has already seen KYC success page", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, viewedKYCSuccessPage: true });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Active"
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: false
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to false if user is Verified but does not have wealthkernel portfolio", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, viewedKYCSuccessPage: false });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {}
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: false
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to false if portfolios are not populated", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, viewedKYCSuccessPage: false });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Active"
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: false
        })
      );
    });

    it("should return the user that made the request with shouldShowKYCSuccessPage field set to true if user is Verified and the user has not already seen KYC success page", async () => {
      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED, viewedKYCSuccessPage: false });
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Active"
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/me?populate=portfolios")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldShowKYCSuccessPage: true
        })
      );
    });

    it("should return the user that made the request with shouldDisplayReferralCodeScreen field set to false if user has already seen referral code page", async () => {
      const user = await buildUser({ viewedReferralCodeScreen: true });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldDisplayReferralCodeScreen: false
        })
      );
    });

    it("should return the user that made the request with shouldDisplayReferralCodeScreen field set to false if user is referred", async () => {
      const user = await buildUser({ viewedReferralCodeScreen: false, referredByEmail: "<EMAIL>" });

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldDisplayReferralCodeScreen: false
        })
      );
    });

    it("should return the user that made the request with shouldDisplayReferralCodeScreen field set to false if user has already submitted passport details", async () => {
      const user = await buildUser({ viewedReferralCodeScreen: false }); // Passport is added by default

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldDisplayReferralCodeScreen: false
        })
      );
    });

    it("should return the user that made the request with shouldDisplayReferralCodeScreen field set to true if user is not referred, has not seen referral code page and is new user", async () => {
      const user = await buildUser({ viewedReferralCodeScreen: false }, true, true);

      const response = await request(app)
        .get("/api/m2m/users/me")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);

      const receivedUser = JSON.parse(response.text);
      expect(receivedUser).toEqual(
        expect.objectContaining({
          _id: user.id,
          shouldDisplayReferralCodeScreen: true
        })
      );
    });

    it("should fail if req.user object is empty", async () => {
      const response = await request(app).get("/api/m2m/users/me").set("Accept", "application/json");
      expect(response.status).toEqual(401);
    });
  });

  describe("GET /users/prompts", () => {
    // This the minimum numbe of banner prompts this method will return
    // This should be updated if the default minimum has changed
    const MINIMUM_BANNER_PROMPTS = 4;
    let user: UserDocument;

    beforeAll(async () => {
      user = await buildUser();
      await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
        savings: new Map([["mmf_dist_gbp", { amount: 1000, currency: "GBX" }]]),
        initialHoldingsAllocation: [
          {
            percentage: 100,
            assetCommonId: "equities_eu"
          }
        ]
      });
      await buildSubscription({ owner: user.id });
      await buildSavingsProduct(true, { commonId: "mmf_dist_gbp" });
    });
    afterAll(async () => await clearDb());

    it("GET /users/prompts?type=modal should return 200 and all prompt modals", async () => {
      // A reward that should NOT be returned in prompts
      await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });

      // A gift and Wealthyhood dividend that should be returned in prompts
      const gift = await (
        await buildGift({
          hasViewedAppModal: false,
          targetUserEmail: user.email
        })
      ).populate("gifter");
      const wealthyhoodDividend = await buildWealthyhoodDividendTransaction({
        hasViewedAppModal: false,
        owner: user.id,
        deposit: {
          activeProviders: [ProviderEnum.WEALTHKERNEL],
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled",
              submittedAt: new Date()
            }
          }
        }
      });

      const response = await request(app)
        .get("/api/m2m/users/prompts?type=modal")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const responseData = JSON.parse(response.text);

      expect(responseData).toEqual({
        modalPrompts: [
          {
            order: 0,
            modalType: "Gift",
            data: { gifts: [JSON.parse(JSON.stringify(gift))] }
          },
          {
            order: 1,
            modalType: "WealthyhoodDividend",
            data: { wealthyhoodDividends: [JSON.parse(JSON.stringify(wealthyhoodDividend))] }
          }
        ]
      });
    });

    it("GET /users/prompts?type=xxx should return 400", async () => {
      const response = await request(app)
        .get("/api/m2m/users/prompts?type=xxx")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("GET /users/prompts?type=banner should return 200 and default banner prompts", async () => {
      const response = await request(app)
        .get("/api/m2m/users/prompts?type=banner")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const responseData = JSON.parse(response.text);
      expect(responseData.bannerPrompts.length).toBeGreaterThanOrEqual(MINIMUM_BANNER_PROMPTS);
    });
  });

  describe("GET /users/me/transaction-activity", () => {
    let user: UserDocument;
    let bankAccount: BankAccountDocument;

    beforeAll(async () => {
      jest.clearAllMocks();
    });
    beforeEach(async () => {
      await clearDb();
      user = await buildUser();
      await buildSubscription({ owner: user.id });
      bankAccount = await buildBankAccount({ owner: user.id });
      await user.populate("bankAccounts");
      await buildPortfolio({ owner: user.id });
    });

    it("should return 400 if the limit is not numeric", async () => {
      const response = await request(app)
        .get("/api/m2m/users/me/transaction-activity?limit=bfae")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should return 400 if the limit is negative", async () => {
      await buildDepositCashTransaction({
        owner: user.id,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          },
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Settled"
          }
        },
        bankAccount: bankAccount.id
      });

      const response = await request(app)
        .get("/api/m2m/users/me/transaction-activity?limit=-40")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    describe("when user has a settled deposit and no limit", () => {
      it("should return the settled deposit", async () => {
        const deposit = await buildDepositCashTransaction({
          owner: user.id,
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const response = await request(app)
          .get("/api/m2m/users/me/transaction-activity")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
      });
    });

    describe("when user has 2 settled deposits but the limit is set to 1", () => {
      it("should return only the newest deposit", async () => {
        await buildDepositCashTransaction({
          owner: user.id,
          createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });

        const deposit2 = await buildDepositCashTransaction({
          owner: user.id,
          createdAt: new Date(),
          providers: {
            truelayer: {
              id: faker.string.uuid(),
              status: "executed"
            },
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Settled"
            }
          },
          bankAccount: bankAccount.id
        });
        const response = await request(app)
          .get("/api/m2m/users/me/transaction-activity?limit=1")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const expectedTransaction = {
          type: "transaction",
          activityFilter: "Deposit",
          item: await DepositCashTransaction.findOne({
            _id: deposit2._id
          })
            .populate("bankAccount")
            .populate("linkedAssetTransaction")
            .populate("linkedSavingsTopup")
        };

        const receivedTransactions = response.body;
        expect(response.status).toEqual(200);
        expect(receivedTransactions).toEqual(
          expect.arrayContaining([expect.objectContaining(JSON.parse(JSON.stringify(expectedTransaction)))])
        );
        expect(receivedTransactions).toHaveLength(1);
      });
    });

    describe("when user has multiple transactions and a limit of 5 is requested", () => {
      it("should return the latest 5 transactions", async () => {
        const [asset7, asset6, asset5, asset1, asset2, asset3, asset4] = await Promise.all([
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 6)
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 5)
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 4)
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: new Date()
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
          }),
          buildAssetTransaction({
            owner: user.id,
            portfolioTransactionCategory: "update",
            status: "Settled",
            createdAt: DateUtil.getDateOfDaysAgo(new Date(), 3)
          })
        ]);

        const response = await request(app)
          .get("/api/m2m/users/me/transaction-activity?limit=3")
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const receivedTransactions = response.body;

        const transactionIDs = receivedTransactions.map((transaction: any) => transaction.item.id);

        expect(transactionIDs).toEqual([asset1.id, asset2.id, asset3.id]);
      });
    });
  });

  describe("GET /users/me/investment-activity", () => {
    let user: UserDocument;

    beforeEach(async () => {
      user = await buildUser();
      await buildPortfolio({ owner: user.id });
      await buildSubscription({ owner: user.id });
    });

    afterEach(async () => await clearDb());

    it("should return empty array when user has no investment activity", async () => {
      const response = await request(app)
        .get("/api/m2m/users/me/investment-activity")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toEqual([]);
    });

    it("should return investment activity items sorted by display date in descending order", async () => {
      // Create transactions and rewards with different dates
      const transaction1 = await buildAssetTransaction({
        owner: user.id,
        portfolioTransactionCategory: "buy",
        status: "Settled",
        settledAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
      });

      const transaction2 = await buildAssetTransaction({
        owner: user.id,
        portfolioTransactionCategory: "buy",
        status: "Settled",
        settledAt: new Date()
      });

      const reward = await buildReward({
        targetUser: user.id,
        accepted: true,
        status: "Settled",
        updatedAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
      });

      // Should not be included in the response
      await buildReward({
        targetUser: user.id,
        accepted: true,
        status: "Pending",
        updatedAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
      });

      const response = await request(app)
        .get("/api/m2m/users/me/investment-activity")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const activityItems = JSON.parse(response.text);

      // Verify items are returned in correct order
      expect(activityItems).toHaveLength(3);
      expect(activityItems[0].item.id).toBe(transaction2.id);
      expect(activityItems[1].item.id).toBe(transaction1.id);
      expect(activityItems[2].item.id).toBe(reward.id);

      // Verify structure of returned items
      expect(activityItems[0]).toEqual(
        expect.objectContaining({
          type: "transaction",
          item: expect.any(Object),
          activityFilter: TransactionInvestmentActivityFilterEnum.Buy
        })
      );

      expect(activityItems[2]).toEqual(
        expect.objectContaining({
          type: "reward",
          item: expect.any(Object),
          activityFilter: RewardInvestmentActivityFilterEnum.Rewards
        })
      );
    });

    it("should return investment activity with correct estimated real time commission fees when user has transactions and WEALTHYHOOD_EUROPE company entity", async () => {
      const ETF_ID = "equities_us";

      let user: UserDocument;
      let portfolio: PortfolioDocument;

      let buyTransactionPending: AssetTransactionDocument;
      let sellTransactionPending: AssetTransactionDocument;
      let sellTransactionSettled: AssetTransactionDocument;

      user = await buildUser({ companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE });
      portfolio = await buildPortfolio({
        owner: user.id,
        holdings: await Promise.all([buildHoldingDTO(true, ETF_ID, 100, { price: 10 })])
      });
      await buildSubscription({ owner: user.id });

      buyTransactionPending = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "update",
        status: "Pending"
      });
      const buyOrderPending = await buildOrder({
        consideration: {
          amount: 10, // stored in cents
          currency: "GBP"
        },
        side: "Buy",
        transaction: buyTransactionPending.id,
        isin: ASSET_CONFIG[ETF_ID].isin
      });

      buyTransactionPending.orders = [buyOrderPending];
      await buyTransactionPending.save();

      sellTransactionPending = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "update",
        status: "Pending",
        createdAt: DateUtil.getDateOfDaysAgo(new Date(), 1)
      });
      const sellOrderPending = await buildOrder({
        quantity: 1,
        side: "Sell",
        transaction: sellTransactionPending.id,
        isin: ASSET_CONFIG[ETF_ID].isin,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
      });

      sellTransactionPending.orders = [sellOrderPending];
      await sellTransactionPending.save();

      sellTransactionSettled = await buildAssetTransaction({
        owner: user.id,
        portfolio: portfolio.id,
        portfolioTransactionCategory: "update",
        status: "Settled",
        settledAt: DateUtil.getDateOfDaysAgo(new Date(), 2)
      });
      const sellOrderSettled = await buildOrder({
        quantity: 1,
        side: "Sell",
        status: "Matched",
        transaction: sellTransactionPending.id,
        isin: ASSET_CONFIG[ETF_ID].isin,
        submissionIntent: OrderSubmissionIntentEnum.REAL_TIME
      });

      sellTransactionSettled.orders = [sellOrderSettled];
      await sellTransactionSettled.save();

      const response = await request(app)
        .get("/api/m2m/users/me/investment-activity")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const activityItems = JSON.parse(response.text);

      // Verify structure of returned items
      expect(activityItems[0]).toEqual(
        expect.objectContaining({
          item: expect.objectContaining({
            status: "Pending",
            id: buyTransactionPending.id
          }),
          activityFilter: TransactionInvestmentActivityFilterEnum.Buy
        })
      );

      expect(activityItems[1]).toEqual(
        expect.objectContaining({
          item: expect.objectContaining({
            status: "Pending",
            id: sellTransactionPending.id
          }),
          activityFilter: TransactionInvestmentActivityFilterEnum.Sell
        })
      );

      expect(activityItems[2]).toEqual(
        expect.objectContaining({
          item: expect.objectContaining({
            status: "Settled",
            id: sellTransactionSettled.id
          }),
          activityFilter: TransactionInvestmentActivityFilterEnum.Sell
        })
      );

      // Buy transaction so it should not have an estimated real time commission fee
      expect(activityItems[0].item.orders[0].estimatedRealTimeCommission).toEqual(undefined);
      // Sell transaction that is PENDING so it should have an estimated real time commission fee
      expect(activityItems[1].item.orders[0].estimatedRealTimeCommission).toEqual(1);
      // Sell transaction that is SETLLED so it should not have an estimated real time commission fee
      expect(activityItems[2].item.orders[0].estimatedRealTimeCommission).toEqual(undefined);
    });
  });

  describe("GET /users/employment-config", () => {
    it("GET /users/employment-config should return 200", async () => {
      const user = await buildUser();

      const response = await request(app)
        .get("/api/m2m/users/employment-config")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const reponseBody = JSON.parse(response.text);
      expect(reponseBody).toEqual({
        employmentStatuses: [
          { id: "FullTime", label: "Full Time" },
          { id: "PartTime", label: "Part Time" },
          { id: "SelfEmployed", label: "Self Employed" },
          { id: "Unemployed", label: "Unemployed" },
          { id: "Retired", label: "Retired" },
          { id: "Student", label: "Student" },
          {
            id: "NotWorkingDueToIllnessOrDisability",
            label: "Not Working Due To Illness Or Disability"
          },
          { id: "CarerOrParent", label: "Carer Or Parent" }
        ],
        employmentStatusThatRequireIndustry: ["FullTime", "PartTime", "SelfEmployed"],
        industries: [
          {
            id: "AgricultureForestryAndFishing",
            label: "Agriculture Forestry And Fishing"
          },
          { id: "ArtsSportAndCreative", label: "Arts Sport And Creative" },
          {
            id: "ConstructionAndEngineering",
            label: "Construction And Engineering"
          },
          {
            id: "CryptoIndustryAndCryptocurrencies",
            label: "Crypto Industry And Cryptocurrencies"
          },
          { id: "CulturalArtefacts", label: "Cultural Artefacts" },
          { id: "DatingOrAdultIndustry", label: "Dating Or Adult Industry" },
          { id: "Education", label: "Education" },
          { id: "EnergyAndWaterSupply", label: "Energy And Water Supply" },
          { id: "FinanceAndInsurance", label: "Finance And Insurance" },
          {
            id: "GamblingOrIGamingIndustry",
            label: "Gambling Or I Gaming Industry"
          },
          {
            id: "GovernmentPublicServiceAndDefence",
            label: "Government Public Service And Defence"
          },
          { id: "HealthAndSocialWork", label: "Health And Social Work" },
          { id: "Hospitality", label: "Hospitality" },
          { id: "ImportAndExport", label: "Import And Export" },
          {
            id: "InformationAndCommunication",
            label: "Information And Communication"
          },
          { id: "LegalAndRegulatory", label: "Legal And Regulatory" },
          { id: "Manufacturing", label: "Manufacturing" },
          { id: "Mining", label: "Mining" },
          { id: "MoneyTransfer", label: "Money Transfer" },
          { id: "MotorTrades", label: "Motor Trades" },
          { id: "PreciousMetals", label: "Precious Metals" },
          { id: "Property", label: "Property" },
          { id: "Retail", label: "Retail" },
          { id: "ScientificAndTechnical", label: "Scientific And Technical" },
          { id: "Tobacco", label: "Tobacco" },
          { id: "TransportAndStorage", label: "Transport And Storage" },
          { id: "Wholesale", label: "Wholesale" }
        ],
        incomeRanges: [
          { id: "1", label: "£0 - £10,000" },
          { id: "2", label: "£10,001 - £20,000" },
          { id: "3", label: "£20,001 - £30,000" },
          { id: "4", label: "£30,001 - £50,000" },
          { id: "5", label: "£50,001 - £75,000" },
          { id: "6", label: "£75,001 - £100,000" },
          { id: "7", label: "£100,001 - £150,000" },
          { id: "8", label: "£150,001 - £250,000" },
          { id: "9", label: "£250,001 - £500,000" },
          { id: "10", label: "£500,001 and above" }
        ],
        sourcesOfWealth: [
          { id: "Salary", label: "Salary" },
          { id: "Inheritance", label: "Inheritance" },
          { id: "Gift", label: "Gift" },
          { id: "BusinessOwnership", label: "Business Ownership" },
          { id: "SaleOfProperty", label: "Sale Of Property" },
          { id: "GamblingOrLottery", label: "Gambling Or Lottery" },
          { id: "PersonalSavings", label: "Personal Savings" },
          { id: "LegalSettlement", label: "Legal Settlement" },
          { id: "SaleOfInvestments", label: "Sale Of Investments" },
          { id: "Dividend", label: "Dividend" }
        ]
      });
    });
  });

  /**
   * ====================
   * TEST POST REQUESTS
   * ====================
   */

  describe("POST /users/me/account-statements/generate", () => {
    it("should return 400 for unverified user", async () => {
      const user = await buildUser({ kycStatus: "failed" });

      await Promise.all([buildPortfolio({ owner: user.id }), buildReward({ targetUser: user.id })]);

      const response = await request(app)
        .post("/api/m2m/users/me/account-statements/generate")
        .set("external-user-id", user.id);

      expect(response.status).toEqual(400);
    });

    it("should return 200 with PDF file for valid request", async () => {
      const CLOUDFLARE_LINK = "https://cloudflare.link";

      const user = await buildUser({ kycStatus: KycStatusEnum.PASSED });

      await Promise.all([
        buildAddress({ owner: user.id }),
        buildPortfolio({ owner: user.id }),
        buildReward({ targetUser: user.id })
      ]);

      jest.spyOn(CloudflareService.Instance, "uploadObject").mockResolvedValue({ fileUri: CLOUDFLARE_LINK });

      const response = await request(app)
        .post("/api/m2m/users/me/account-statements/generate")
        .set("external-user-id", user.id);

      expect(response.status).toEqual(200);
      expect(JSON.parse(response.text)).toMatchObject({
        fileUri: CLOUDFLARE_LINK
      });
    });
  });

  describe("POST /users/verify", () => {
    describe("user ID header is missing", () => {
      beforeAll(_startup);
      afterAll(_teardown);
      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/verify")
          .send({})
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("user ID header is not ObjectID", () => {
      beforeAll(_startup);
      afterAll(_teardown);
      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/verify")
          .send({})
          .set("external-user-id", "invalid-user-id")
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("user has not submitted all required information", () => {
      describe("user has no employment info", () => {
        let user: UserDocument;
        beforeAll(async () => {
          _startup();
          user = await buildUser({
            employmentInfo: undefined
          });
        });
        afterAll(_teardown);
        it("should return 400 with user not submitted required information error", async () => {
          const actualResponse = await request(app)
            .post("/api/m2m/users/verify")
            .send({})
            .set("external-user-id", user._id)
            .set("Accept", "application/json");

          expect(actualResponse.status).toEqual(400);
          expect(JSON.parse(actualResponse.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Operation failed",
                message: "User has not submitted all required information"
              }
            })
          );
        });
      });
    });

    describe("user has no tax residency details and but tax residency verification is required", () => {
      let user: UserDocument;
      beforeAll(async () => {
        _startup();
        user = await buildUser({
          taxResidency: undefined,
          residencyCountry: "GB"
        });
        await buildAddress({ owner: user._id });
      });
      afterAll(_teardown);
      it("should return 400 with user not submitted required information error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/verify")
          .send({})
          .set("external-user-id", user._id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(400);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Operation failed",
              message: "User has not submitted all required information"
            }
          })
        );
      });
    });

    describe("user is already verified", () => {
      let user: UserDocument;
      let actualResponse: request.Response;
      beforeAll(async () => {
        _startup();
        user = await buildUser({ kycStatus: KycStatusEnum.PASSED });
        await buildAddress({ owner: user._id });
        await buildPortfolio({
          owner: user._id,
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
        });
        actualResponse = await request(app)
          .post("/api/m2m/users/verify")
          .send({})
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);
      it("should return 200 with verified status", async () => {
        expect(actualResponse.status).toEqual(200);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            status: "verified"
          })
        );
      });
      it("should not emit event", async () => {
        expect(eventEmitter.emit).toBeCalledTimes(0);
      });
    });

    describe("user has UK company entity and is not already verified", () => {
      const wkCreatePartyResponse = { id: faker.string.uuid() };
      const wkCreateW8BenFormResponse = { id: faker.string.uuid() };
      const wkCreateAddressResponse = { id: faker.string.uuid() };
      const wkCreateAccountResponse = { id: faker.string.uuid() };
      const wkCreatePortfolioResponse = { id: faker.string.uuid() };

      describe("all other verification steps are done but", () => {
        describe("user does not have a Stripe customer ID in our database", () => {
          describe("user does not have a Stripe customer submitted", () => {
            const stripeCreateCustomerResponse = { id: faker.string.uuid() };

            let user: UserDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(StripeService.Instance, "createCustomer").mockResolvedValue(stripeCreateCustomerResponse);
              jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.UKInstance, "createParty").mockResolvedValue(wkCreatePartyResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING
              });
              await buildAddress({ owner: user._id });
              await _mockSuccessfulSumsubPassportVerification(user);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit customer to Stripe", async () => {
              expect(StripeService.Instance.createCustomer).toBeCalledWith({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                metadata: {
                  wealthyhoodId: user.id
                }
              });
              expect(StripeService.Instance.createCustomer).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user._id);
              expect(updatedUser?.providers?.stripe?.id).toEqual(stripeCreateCustomerResponse.id);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("user does not have a Wealthkernel party ID in our database", () => {
          describe("user does not have a Wealthkernel party submitted", () => {
            let user: UserDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "retrieveParties").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.UKInstance, "createParty").mockResolvedValue(wkCreatePartyResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await buildAddress({ owner: user._id });
              await _mockSuccessfulSumsubPassportVerification(user);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit party to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createParty).toBeCalledWith({
                type: "Person",
                forename: user.firstName,
                surname: user.lastName,
                emailAddress: user.email,
                dateOfBirth: WealthkernelService.formatDate(user.dateOfBirth as Date),
                nationalities: user.nationalities.map(
                  (nationality) => nationality as countriesConfig.CountryCodesType
                ),
                taxResidencies: [user.taxResidency.countryCode as countriesConfig.CountryCodesType],
                identifiers: [
                  {
                    type: "NINO" as DocumentCodesType,
                    value: user.taxResidency.value,
                    issuer: "GB" as countriesConfig.CountryCodesType
                  }
                ],
                annualIncome: {
                  amount: user.employmentInfo.annualIncome.amount,
                  currency: user.employmentInfo.annualIncome.currency
                },
                employmentStatus: user.employmentInfo.employmentStatus,
                sourcesOfWealth: user.employmentInfo.sourcesOfWealth,
                industry: user.employmentInfo?.industry
              });
              expect(WealthkernelService.UKInstance.createParty).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user._id);
              expect(updatedUser?.providers?.wealthkernel?.id).toEqual(wkCreatePartyResponse.id);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel party submitted", () => {
            let user: UserDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse = buildWealthkernelAccountResponse({ status: "Pending" });
            const wklistAdressesResponse = buildWealthkernelAddressResponse();
            const wkRetrievePartiesResponse = buildWealthkernelPartyResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveParties")
                .mockResolvedValue([wkRetrievePartiesResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "listAddresses")
                .mockResolvedValue([wklistAdressesResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "createW8BenForm")
                .mockResolvedValue(wkCreateW8BenFormResponse);
              jest.spyOn(WealthkernelService.UKInstance, "createParty");

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              const account: AccountDocument = await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              await _mockSuccessfulSumsubPassportVerification(user);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not create new Wealthkernel party", async () => {
              expect(WealthkernelService.UKInstance.createParty).toBeCalledTimes(0);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user._id);
              expect(updatedUser?.providers?.wealthkernel?.id).toEqual(wkRetrievePartiesResponse.id);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("reaching out to Wealthkernel fails", () => {
            const wkRetrievePartiesResponse = buildWealthkernelPartyResponse();

            let user: UserDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "createParty");
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveParties")
                .mockResolvedValue([wkRetrievePartiesResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts").mockImplementation(async () => {
                throw Error("Some error");
              });

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await buildAddress({ owner: user._id });
              const account: AccountDocument = await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              await _mockSuccessfulSumsubPassportVerification(user);
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not create new Wealthkernel party", async () => {
              expect(WealthkernelService.UKInstance.createParty).toBeCalledTimes(0);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should return 500", async () => {
              expect(actualResponse.status).toEqual(500);
            });
          });
        });

        describe("user does not have a Wealthkernel address in our database", () => {
          describe("user does not have a Wealthkernel address submitted", () => {
            let user: UserDocument;
            let address: AddressDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "listAddresses").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.UKInstance, "addAddress").mockResolvedValue(wkCreateAddressResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid()
                  },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              address = await buildAddress({ owner: user._id });
              await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit address to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.addAddress).toBeCalledWith({
                city: address.city,
                countryCode: address.countryCode,
                line1: address.line1,
                line2: undefined,
                line3: undefined,
                partyId: user.providers.wealthkernel.id,
                postalCode: address.postalCode
              });
              expect(WealthkernelService.UKInstance.addAddress).toBeCalledTimes(1);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedAddress = (await Address.findOne({ owner: user._id })) as AddressDocument;
              expect(updatedAddress.toObject()).toEqual(
                expect.objectContaining({
                  providers: Object.fromEntries([
                    [
                      ProviderEnum.WEALTHKERNEL,
                      {
                        id: wkCreateAddressResponse.id
                      }
                    ]
                  ])
                })
              );
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel address submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkListAddressesResponse: AddressType = buildWealthkernelAddressResponse();
            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse({
              status: "Pending"
            });

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "listAddresses")
                .mockResolvedValue([wkListAddressesResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "addAddress");
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "createW8BenForm")
                .mockResolvedValue(wkCreateW8BenFormResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid()
                  },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await buildAddress({ owner: user._id });
              account = await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              await _mockSuccessfulSumsubPassportVerification(user);
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not submit address to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.addAddress).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedAddress = (await Address.findOne({ owner: user._id })) as AddressDocument;
              expect(updatedAddress.toObject()).toEqual(
                expect.objectContaining({
                  providers: Object.fromEntries([
                    [
                      ProviderEnum.WEALTHKERNEL,
                      {
                        id: wkListAddressesResponse.id
                      }
                    ]
                  ])
                })
              );
            });

            it("should not emit event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("user does not have a Wealthkernel account ID in our database", () => {
          describe("user does not have a Wealthkernel account submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts").mockResolvedValue([]);
              jest
                .spyOn(WealthkernelService.UKInstance, "createAccount")
                .mockResolvedValue(wkCreateAccountResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              account = await buildAccount({
                owner: user._id
              });
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit account to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createAccount).toBeCalledWith({
                type: account.wrapperType,
                name: account.name,
                productId: "prd-gia",
                owner: user.providers.wealthkernel.id,
                clientReference: account.id,
                currency: "GBP"
              });
              expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(1);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedAccount = (await Account.findOne({
                owner: user._id
              })) as AccountDocument;
              expect(updatedAccount.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
              expect(updatedAccount.providers?.wealthkernel?.status).toEqual("Pending");
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel account submitted and is pending", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse({
              status: "Pending"
            });

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "createAccount");

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              account = await buildAccount({
                owner: user._id
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should retrieve WK accounts using party ID", async () => {
              expect(WealthkernelService.UKInstance.retrieveAccounts).toHaveBeenCalledWith({
                owner: user.providers.wealthkernel.id
              });
            });

            it("should not submit account to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(0);
            });

            it("should not emit event", async () => {
              expect(eventEmitter.emit).not.toHaveBeenCalled();
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel account submitted and is active", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "createAccount");

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              account = await buildAccount({
                owner: user._id
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not submit account to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedAccount = (await Account.findOne({
                owner: user._id
              })) as AccountDocument;
              expect(updatedAccount.providers?.wealthkernel?.id).toEqual(wkRetrieveAccountsResponse.id);
              expect(updatedAccount.providers?.wealthkernel?.status).toEqual("Active");
            });

            it("should return 200 with verified status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });

          describe("reaching out to Wealthkernel fails", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts").mockImplementation(async () => {
                throw Error("Some error");
              });
              jest.spyOn(WealthkernelService.UKInstance, "createAccount");

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id });
              account = await buildAccount({
                owner: user._id
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not submit account to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(0);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should return 500", async () => {
              expect(actualResponse.status).toEqual(500);
            });
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");

            user = await buildUser(
              {
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should not retrieve accounts from Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.retrieveAccounts).toBeCalledTimes(0);
          });

          it("should not submit account to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(0);
          });

          it("should not update our model", async () => {
            const updatedAccount = (await Account.findOne({ owner: user._id })) as AccountDocument;
            expect(updatedAccount.providers?.wealthkernel?.id).toEqual(account.providers?.wealthkernel?.id);
            expect(updatedAccount.providers?.wealthkernel?.status).toEqual(
              account.providers?.wealthkernel?.status
            );
          });

          it("should emit event", async () => {
            expect(eventEmitter.emit).toHaveBeenCalledWith(
              events.user.verification.eventId,
              expect.objectContaining({ id: user.id }),
              expect.objectContaining({ emailNotification: false })
            );
          });

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database but there is another user with the same NINo", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const nationalInsuranceNumber = faker.string.uuid();
            await buildUser({
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: nationalInsuranceNumber
              }
            });
            user = await buildUser(
              {
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                taxResidency: {
                  countryCode: "GB",
                  proofType: "NINO",
                  value: nationalInsuranceNumber
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });

          it("should flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).toBe(true);
          });

          it("should not submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database, there is another user with the same NINo, but the user e-mail prefix is exempt", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const nationalInsuranceNumber = faker.string.uuid();
            await buildUser({
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: nationalInsuranceNumber
              }
            });
            user = await buildUser(
              {
                email: "<EMAIL>",
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                taxResidency: {
                  countryCode: "GB",
                  proofType: "NINO",
                  value: nationalInsuranceNumber
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });

          it("should not flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).not.toBe(true);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database, there is another user with the same NINo, but the user e-mail suffix is exempt", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const nationalInsuranceNumber = faker.string.uuid();
            await buildUser({
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: nationalInsuranceNumber
              }
            });
            user = await buildUser(
              {
                email: "<EMAIL>",
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                taxResidency: {
                  countryCode: "GB",
                  proofType: "NINO",
                  value: nationalInsuranceNumber
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });

          it("should not flag the user as potentially duplicate", async () => {
            const updatedUser = (await User.findById(user._id)) as UserDocument;
            expect(updatedUser.isPotentiallyDuplicateAccount).not.toBe(true);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database but there is another user with the last name and date of birth", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const lastName = faker.person.lastName();
            const dateOfBirth = faker.date.past();
            await buildUser({
              lastName,
              dateOfBirth,
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: faker.string.uuid()
              }
            });
            user = await buildUser(
              {
                lastName,
                dateOfBirth,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });

          it("should flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).toBe(true);
          });

          it("should not submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database but there is another user with the last name (but with different casing) and date of birth", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const lastName = faker.person.lastName();
            const dateOfBirth = faker.date.past();
            await buildUser({
              lastName: lastName.toLowerCase(),
              dateOfBirth,
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: faker.string.uuid()
              }
            });
            user = await buildUser(
              {
                lastName: lastName.toUpperCase(),
                dateOfBirth,
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });

          it("should flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).toBe(true);
          });

          it("should not submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database but there is another user with the same name and address", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const firstName = faker.person.lastName();
            const lastName = faker.person.lastName();
            const dateOfBirth = faker.date.past();
            const userWithTheSameName = await buildUser({
              firstName: firstName.toLowerCase(),
              lastName: lastName.toLowerCase(),
              dateOfBirth,
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: faker.string.uuid()
              }
            });
            user = await buildUser(
              {
                firstName: firstName.toUpperCase(),
                lastName: lastName.toUpperCase(),
                dateOfBirth,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildAddress({
              owner: userWithTheSameName._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });

          it("should flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).toBe(true);
          });

          it("should not submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
          });
        });

        describe("user has a Wealthkernel account ID with status 'Active' in our database but there is another user with the same name, date of birth and address BUT different company entity", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          beforeAll(async () => {
            _startup();
            jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

            const firstName = faker.person.lastName();
            const lastName = faker.person.lastName();
            const dateOfBirth = faker.date.past();
            const userWithTheSameName = await buildUser({
              firstName: firstName.toLowerCase(),
              lastName: lastName.toLowerCase(),
              dateOfBirth,
              companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: faker.string.uuid()
              }
            });
            user = await buildUser(
              {
                firstName: firstName.toUpperCase(),
                lastName: lastName.toUpperCase(),
                dateOfBirth,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              },
              false
            );
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Active"
                }
              }
            });
            await buildAddress({
              owner: userWithTheSameName._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await buildBankAccount({
              owner: user._id,
              providers: { wealthkernel: { id: faker.string.uuid() } }
            });
            await user.populate("bankAccounts");

            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });

          it("should NOT flag the user as potentially duplicate", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser.isPotentiallyDuplicateAccount).toBeUndefined();
          });
        });

        describe("user has a pending account in our database but it is active in Wealthkernel", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
              .mockResolvedValue([wkRetrieveAccountsResponse]);
            jest.spyOn(WealthkernelService.UKInstance, "createAccount");

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              w8BenForm: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                completedAt: new Date(),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              providers: {
                wealthkernel: { id: faker.string.uuid() },
                stripe: {
                  id: faker.string.uuid()
                }
              }
            });
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: wkRetrieveAccountsResponse.id,
                  status: "Pending"
                }
              }
            });
            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should not submit account to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createAccount).toBeCalledTimes(0);
          });

          it("should update our account model", async () => {
            const updatedAccount = (await Account.findOne({ owner: user._id })) as AccountDocument;
            expect(updatedAccount.providers?.wealthkernel?.id).toEqual(wkRetrieveAccountsResponse.id);
            expect(updatedAccount.providers?.wealthkernel?.status).toEqual("Active");
          });

          it("should set user's kyc passed flag to true", async () => {
            const updatedUser = (await User.findById(user._id)) as UserDocument;
            expect(updatedUser.hasPassedKyc).toEqual(true);
          });

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });
        });

        describe("user does not have a Wealthkernel W-8BEN form in our database", () => {
          describe("user does not have a Wealthkernel W-8BEN form submitted", () => {
            let user: UserDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "createW8BenForm")
                .mockResolvedValue(wkCreateW8BenFormResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid()
                  },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL]
                }
              });
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should create W-8BEN form in Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledWith({
                partyId: user.providers.wealthkernel.id
              });
              expect(WealthkernelService.UKInstance.createW8BenForm).toHaveBeenCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user.id);
              expect(updatedUser.toObject()).toEqual(
                expect.objectContaining({
                  w8BenForm: expect.objectContaining({
                    activeProviders: [ProviderEnum.WEALTHKERNEL],
                    providers: expect.objectContaining({
                      wealthkernel: {
                        id: wkCreateW8BenFormResponse.id,
                        status: "Pending"
                      }
                    })
                  })
                })
              );
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel W-8BEN form submitted but not completed", () => {
            let user: UserDocument;
            let actualResponse: request.Response;

            const W_8BEN_FORM_ID = faker.string.uuid();
            const W_8BEN_FORM_COMPLETED_AT = new Date();

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "createW8BenForm");
              jest
                .spyOn(WealthkernelService.UKInstance, "completeW8BenForm")
                .mockResolvedValue({ id: W_8BEN_FORM_ID });

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: W_8BEN_FORM_COMPLETED_AT,
                  providers: {
                    wealthkernel: {
                      id: W_8BEN_FORM_ID,
                      status: "Pending"
                    }
                  }
                },
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid()
                  },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              const account = await buildAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should complete W-8BEN form in Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createW8BenForm).not.toHaveBeenCalled();
              expect(WealthkernelService.UKInstance.completeW8BenForm).toBeCalledWith(
                user.w8BenForm.providers.wealthkernel.id,
                {
                  completedAt: W_8BEN_FORM_COMPLETED_AT.toISOString()
                }
              );
              expect(WealthkernelService.UKInstance.completeW8BenForm).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user.id);
              expect(updatedUser.toObject()).toEqual(
                expect.objectContaining({
                  w8BenForm: expect.objectContaining({
                    activeProviders: [ProviderEnum.WEALTHKERNEL],
                    providers: expect.objectContaining({
                      wealthkernel: {
                        id: user.w8BenForm.providers.wealthkernel.id,
                        status: "Completed"
                      }
                    })
                  })
                })
              );
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });
        });

        describe("user passport details do not match Sumsub", () => {
          let user: UserDocument;
          let actualResponse: request.Response;

          const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
              .mockResolvedValue([wkRetrieveAccountsResponse]);
            jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              w8BenForm: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                completedAt: new Date(),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              providers: {
                wealthkernel: { id: faker.string.uuid() },
                stripe: {
                  id: faker.string.uuid()
                },
                sumsub: {
                  id: faker.string.uuid()
                }
              }
            });
            const [account] = await Promise.all([
              buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id,
                    status: "Active"
                  }
                }
              }),
              buildAddress({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id
                  }
                }
              }),
              buildKycOperation({
                owner: user.id,
                activeProviders: [ProviderEnum.SUMSUB],
                providers: {
                  sumsub: {
                    id: faker.string.uuid(),
                    status: "completed",
                    decision: "GREEN"
                  }
                }
              })
            ]);
            await buildPortfolio({ owner: user._id, account: account._id }, false);

            jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(buildApplicant());

            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should not submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
          });

          it("should flag the user and change kycStatus to 'failed'", async () => {
            const updatedUser = await User.findById(user.id);

            expect(updatedUser?.isPassportVerified).toStrictEqual(false);
            expect(updatedUser?.kycStatus).toStrictEqual(KycStatusEnum.FAILED);
          });

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });
        });

        describe("user passport details match Sumsub but with different casing", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
              .mockResolvedValue([wkRetrieveAccountsResponse]);
            jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolios").mockResolvedValue([]);
            jest
              .spyOn(WealthkernelService.UKInstance, "createPortfolio")
              .mockResolvedValue(wkCreatePortfolioResponse);

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1),
              w8BenForm: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                completedAt: new Date(),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              providers: {
                wealthkernel: { id: faker.string.uuid() },
                stripe: {
                  id: faker.string.uuid()
                }
              }
            });
            [account] = await Promise.all([
              buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id,
                    status: "Active"
                  }
                }
              }),
              buildAddress({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id
                  }
                }
              }),
              buildKycOperation({
                owner: user.id,
                activeProviders: [ProviderEnum.SUMSUB],
                providers: {
                  sumsub: {
                    id: faker.string.uuid(),
                    status: "completed",
                    decision: "GREEN"
                  }
                }
              })
            ]);
            await buildPortfolio({ owner: user._id, account: account._id }, false);

            jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
              buildApplicant({
                info: {
                  firstName: user.firstName.toUpperCase(),
                  lastName: user.lastName.toUpperCase(),
                  dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
                  nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]]
                }
              })
            );

            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledWith({
              accountId: account.providers?.wealthkernel?.id,
              name: "General Investment Portfolio",
              currency: CurrencyEnum.GBP,
              mandate: {
                type: "ExecutionOnlyMandate"
              }
            });
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(1);
          });

          it("should emit event", async () => {
            expect(eventEmitter.emit).toBeCalledWith(
              events.user.verification.eventId,
              expect.objectContaining({ id: user.id }),
              expect.objectContaining({ emailNotification: false })
            );
          });

          it("should update our model", async () => {
            const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
            expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkCreatePortfolioResponse.id);
          });

          it("should not flag the user", async () => {
            const updatedUser = await User.findById(user.id);

            expect(updatedUser?.isPassportVerified).toStrictEqual(true);
          });

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });
        });

        describe("user has a pending KYC operation but it's passed in Sumsub", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let kycOperation: KycOperationDocument;
          let actualResponse: request.Response;

          const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
              .mockResolvedValue([wkRetrieveAccountsResponse]);
            jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolios").mockResolvedValue([]);
            jest
              .spyOn(WealthkernelService.UKInstance, "createPortfolio")
              .mockResolvedValue(wkCreatePortfolioResponse);

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              providers: {
                wealthkernel: { id: faker.string.uuid() },
                stripe: {
                  id: faker.string.uuid()
                }
              },
              w8BenForm: {
                activeProviders: [ProviderEnum.WEALTHKERNEL],
                completedAt: new Date(),
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Completed"
                  }
                }
              },
              submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1)
            });
            kycOperation = await buildKycOperation({
              owner: user.id,
              activeProviders: [ProviderEnum.SUMSUB],
              providers: {
                sumsub: {
                  id: faker.string.uuid(),
                  status: "pending"
                }
              }
            });
            await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: wkRetrieveAccountsResponse.id,
                  status: "Active"
                }
              }
            });
            await buildPortfolio({ owner: user._id, account: account._id }, false);

            jest.spyOn(SumsubService.Instance, "retrieveApplicant").mockResolvedValue(
              buildApplicant({
                review: {
                  reviewId: kycOperation.providers.sumsub.id,
                  reviewStatus: "completed",
                  reviewResult: {
                    reviewAnswer: "GREEN"
                  }
                },
                info: {
                  firstName: user.firstName,
                  lastName: user.lastName,
                  dob: DateUtil.formatDateToYYYYMMDD(user.dateOfBirth),
                  nationality: countriesConfig.TWO_TO_THREE_COUNTRY_CODE_MAPPING[user.nationalities[0]]
                }
              })
            );

            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should sync the kyc operation", async () => {
            const updatedKycOperation = await KycOperation.findById(kycOperation.id);
            expect(updatedKycOperation?.toObject().providers.sumsub).toEqual(
              expect.objectContaining({
                id: kycOperation.providers.sumsub.id,
                status: "completed",
                decision: "GREEN"
              })
            );
          });

          it("should submit portfolio to Wealthkernel", async () => {
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledWith({
              accountId: account.providers?.wealthkernel?.id,
              name: "General Investment Portfolio",
              currency: CurrencyEnum.GBP,
              mandate: {
                type: "ExecutionOnlyMandate"
              }
            });
            expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(1);
          });

          it("should emit event", async () => {
            expect(eventEmitter.emit).toBeCalledWith(
              events.user.verification.eventId,
              expect.objectContaining({ id: user.id }),
              expect.objectContaining({ emailNotification: false })
            );
          });

          it("should update our model", async () => {
            const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
            expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkCreatePortfolioResponse.id);
          });

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });
        });

        describe("user does not have a Wealthkernel portfolio ID in our database", () => {
          describe("user does not have a Wealthkernel portfolio submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolios").mockResolvedValue([]);
              jest
                .spyOn(WealthkernelService.UKInstance, "createPortfolio")
                .mockResolvedValue(wkCreatePortfolioResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PASSED,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1)
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id,
                    status: "Active"
                  }
                }
              });
              await buildPortfolio({ owner: user._id, account: account._id }, false);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit portfolio to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledWith({
                accountId: account.providers?.wealthkernel?.id,
                name: "General Investment Portfolio",
                currency: CurrencyEnum.GBP,
                mandate: {
                  type: "ExecutionOnlyMandate"
                }
              });
              expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(1);
            });

            it("should emit event", async () => {
              expect(eventEmitter.emit).toBeCalledWith(
                events.user.verification.eventId,
                expect.objectContaining({ id: user.id }),
                expect.objectContaining({ emailNotification: false })
              );
            });

            it("should update our model", async () => {
              const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
              expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkCreatePortfolioResponse.id);
            });

            it("should return 200 with verified status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });

          describe("user has a Wealthkernel portfolio submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountResponse: AccountType = buildWealthkernelAccountResponse();
            const wkRetrievePortfoliosResponse: PortfolioType = buildWealthkernelPortfoliosResponse();
            const wklistAdressesResponse: AddressType = buildWealthkernelAddressResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "retrievePortfolios")
                .mockResolvedValue([wkRetrievePortfoliosResponse]);
              jest
                .spyOn(WealthkernelService.UKInstance, "listAddresses")
                .mockResolvedValue([wklistAdressesResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

              user = await buildUser({
                kycStatus: KycStatusEnum.PASSED,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                },
                submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1)
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountResponse.id,
                    status: "Active"
                  }
                }
              });
              await buildPortfolio({ owner: user._id, account: account._id }, false);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);
            it("should not submit portfolio to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createPortfolio).toBeCalledTimes(0);
            });
            it("should emit event", async () => {
              expect(eventEmitter.emit).toBeCalledWith(
                events.user.verification.eventId,
                expect.objectContaining({ id: user.id }),
                expect.objectContaining({ emailNotification: false })
              );
            });
            it("should sync our model", async () => {
              const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
              expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkRetrievePortfoliosResponse.id);
            });
            it("should return 200 with verified status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });

          describe("reaching out to Wealthkernel fails", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.UKInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.UKInstance, "retrievePortfolios").mockImplementation(async () => {
                throw Error("Some error");
              });
              jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

              user = await buildUser({
                kycStatus: KycStatusEnum.PASSED,
                w8BenForm: {
                  activeProviders: [ProviderEnum.WEALTHKERNEL],
                  completedAt: new Date(),
                  providers: {
                    wealthkernel: {
                      id: faker.string.uuid(),
                      status: "Completed"
                    }
                  }
                },
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id,
                    status: "Active"
                  }
                }
              });
              await buildPortfolio({ owner: user._id, account: account._id }, false);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should not submit portfolio to Wealthkernel", async () => {
              expect(WealthkernelService.UKInstance.createPortfolio).not.toHaveBeenCalled();
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should return 500", async () => {
              expect(actualResponse.status).toEqual(500);
            });
          });
        });
      });
    });

    describe("user has EU company entity and is not already verified", () => {
      describe("all other verification steps are done but", () => {
        describe("user does not have a Stripe customer ID in our database", () => {
          describe("user does not have a Stripe customer submitted", () => {
            const stripeCreateCustomerResponse = { id: faker.string.uuid() };

            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrievePartiesResponse = buildWealthkernelPartyResponse({
              id: process.env.WEALTHYHOOD_EUROPE_WK_PARTY
            });
            const wkCreateAccountResponse = { id: faker.string.uuid() };

            beforeAll(async () => {
              _startup();
              jest.spyOn(StripeService.Instance, "createCustomer").mockResolvedValue(stripeCreateCustomerResponse);
              jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer");
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveParties")
                .mockResolvedValue([wkRetrievePartiesResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.EUInstance, "addAddress");
              jest.spyOn(WealthkernelService.EUInstance, "createParty");
              jest
                .spyOn(WealthkernelService.EUInstance, "createAccount")
                .mockResolvedValue(wkCreateAccountResponse);

              user = await buildUser({
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                kycStatus: KycStatusEnum.PENDING,
                activeProviders: [
                  ProviderEnum.GOCARDLESS,
                  ProviderEnum.WEALTHKERNEL,
                  ProviderEnum.STRIPE,
                  ProviderEnum.SALTEDGE
                ],
                providers: {
                  gocardless: {
                    id: faker.string.uuid()
                  },
                  saltedge: {
                    id: faker.string.uuid()
                  }
                }
              });

              [, account] = await Promise.all([
                buildAddress({ owner: user._id, activeProviders: [] }),
                buildAccount({
                  owner: user._id
                })
              ]);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit customer to Stripe", async () => {
              expect(StripeService.Instance.createCustomer).toBeCalledWith({
                email: user.email,
                name: `${user.firstName} ${user.lastName}`,
                metadata: {
                  wealthyhoodId: user.id
                }
              });
              expect(StripeService.Instance.createCustomer).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user._id);
              expect(updatedUser?.providers?.stripe?.id).toEqual(stripeCreateCustomerResponse.id);
            });

            it("should NOT submit customer to GoCardless", async () => {
              expect(GoCardlessPaymentsService.Instance.createCustomer).not.toHaveBeenCalled();
            });

            it("should submit account to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createAccount).toHaveBeenCalledWith({
                clientReference: account.id,
                currency: user.currency,
                name: "General Investment Account",
                owner: process.env.WEALTHYHOOD_EUROPE_WK_PARTY,
                productId: "prd-gia-corporate",
                type: "GIA"
              });
            });

            it("should update the account document with the newly created account", async () => {
              const updatedAccount = await Account.findOne({ owner: user._id });
              expect(updatedAccount?.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("user does not have a Saltedge lead ID in our database", () => {
          describe("user does not have a Saltedge lead submitted", () => {
            const saltedgeCreateLeadResponse = {
              data: {
                customer_id: faker.string.uuid()
              }
            };

            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrievePartiesResponse = buildWealthkernelPartyResponse({
              id: process.env.WEALTHYHOOD_EUROPE_WK_PARTY
            });
            const wkCreateAccountResponse = { id: faker.string.uuid() };

            beforeAll(async () => {
              _startup();
              jest.spyOn(SaltedgeService.Instance, "createLead").mockResolvedValue(saltedgeCreateLeadResponse);
              jest.spyOn(GoCardlessPaymentsService.Instance, "createCustomer");
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveParties")
                .mockResolvedValue([wkRetrievePartiesResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.EUInstance, "addAddress");
              jest.spyOn(WealthkernelService.EUInstance, "createParty");
              jest
                .spyOn(WealthkernelService.EUInstance, "createAccount")
                .mockResolvedValue(wkCreateAccountResponse);

              user = await buildUser({
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                kycStatus: KycStatusEnum.PENDING,
                activeProviders: [
                  ProviderEnum.GOCARDLESS,
                  ProviderEnum.WEALTHKERNEL,
                  ProviderEnum.STRIPE,
                  ProviderEnum.SALTEDGE
                ],
                providers: {
                  gocardless: {
                    id: faker.string.uuid()
                  },
                  stripe: {
                    id: faker.string.uuid()
                  }
                }
              });

              [, account] = await Promise.all([
                buildAddress({ owner: user._id, activeProviders: [] }),
                buildAccount({
                  owner: user._id
                })
              ]);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit customer to Saltedge", async () => {
              expect(SaltedgeService.Instance.createLead).toBeCalledWith({
                fullName: user.fullName,
                email: user.email,
                identifier: user.id
              });
              expect(SaltedgeService.Instance.createLead).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedUser = await User.findById(user._id);
              expect(updatedUser?.providers?.saltedge?.id).toEqual(saltedgeCreateLeadResponse.data.customer_id);
            });

            it("should NOT submit customer to GoCardless", async () => {
              expect(GoCardlessPaymentsService.Instance.createCustomer).not.toHaveBeenCalled();
            });

            it("should submit account to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createAccount).toHaveBeenCalledWith({
                clientReference: account.id,
                currency: user.currency,
                name: "General Investment Account",
                owner: process.env.WEALTHYHOOD_EUROPE_WK_PARTY,
                productId: "prd-gia-corporate",
                type: "GIA"
              });
            });

            it("should update the account document with the newly created account", async () => {
              const updatedAccount = await Account.findOne({ owner: user._id });
              expect(updatedAccount?.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("wallet does not have a Devengo account ID in our database", () => {
          describe("wallet does not have a Devengo account submitted", () => {
            const devengoCreateAccountResponse = {
              account: {
                id: faker.string.uuid(),
                status: "created" as AccountStatusType,
                identifiers: [
                  {
                    type: "iban",
                    iban: faker.finance.iban()
                  } as IdentifierType
                ]
              }
            };

            let user: UserDocument;
            let wallet: WalletDocument;
            let actualResponse: request.Response;

            const wkRetrievePartiesResponse = buildWealthkernelPartyResponse({
              id: process.env.WEALTHYHOOD_EUROPE_WK_PARTY
            });
            const wkCreateAccountResponse = { id: faker.string.uuid() };

            beforeAll(async () => {
              _startup();
              jest.spyOn(DevengoService.Instance, "createAccount").mockResolvedValue(devengoCreateAccountResponse);
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveParties")
                .mockResolvedValue([wkRetrievePartiesResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
              jest.spyOn(WealthkernelService.EUInstance, "addAddress");
              jest.spyOn(WealthkernelService.EUInstance, "createParty");
              jest
                .spyOn(WealthkernelService.EUInstance, "createAccount")
                .mockResolvedValue(wkCreateAccountResponse);

              user = await buildUser({
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                kycStatus: KycStatusEnum.PENDING,
                activeProviders: [ProviderEnum.GOCARDLESS, ProviderEnum.WEALTHKERNEL, ProviderEnum.STRIPE],
                providers: {
                  stripe: {
                    id: faker.string.uuid()
                  },
                  gocardless: {
                    id: faker.string.uuid()
                  }
                }
              });

              [, , wallet] = await Promise.all([
                buildAddress({ owner: user._id, activeProviders: [] }),
                buildAccount({
                  owner: user._id
                }),
                buildWallet({ owner: user.id, activeProviders: [ProviderEnum.DEVENGO] })
              ]);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit account to Devengo", async () => {
              expect(DevengoService.Instance.createAccount).toBeCalledWith({
                name: user.id,
                currency: "EUR",
                metadata: {
                  wealthyhoodId: wallet.id
                }
              });
              expect(DevengoService.Instance.createAccount).toBeCalledTimes(1);
            });

            it("should update our model", async () => {
              const updatedWallet = await Wallet.findById(wallet._id);
              expect(updatedWallet.toObject()).toEqual(
                expect.objectContaining({
                  iban: devengoCreateAccountResponse.account.identifiers[0].iban,
                  providers: {
                    devengo: {
                      id: devengoCreateAccountResponse.account.id,
                      status: devengoCreateAccountResponse.account.status
                    }
                  }
                })
              );
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("user does not have a GoCardless customer ID in our database", () => {
          const GOCARDLESS_CUSTOMER_ID = faker.string.uuid();

          let user: UserDocument;
          let address: AddressDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          const wkRetrievePartiesResponse = buildWealthkernelPartyResponse({
            id: process.env.WEALTHYHOOD_EUROPE_WK_PARTY
          });
          const wkCreateAccountResponse = { id: faker.string.uuid() };

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(GoCardlessPaymentsService.Instance, "createCustomer")
              .mockResolvedValue({ id: GOCARDLESS_CUSTOMER_ID });

            jest
              .spyOn(WealthkernelService.EUInstance, "retrieveParties")
              .mockResolvedValue([wkRetrievePartiesResponse]);
            jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
            jest.spyOn(WealthkernelService.EUInstance, "addAddress");
            jest.spyOn(WealthkernelService.EUInstance, "createParty");
            jest.spyOn(WealthkernelService.EUInstance, "createAccount").mockResolvedValue(wkCreateAccountResponse);

            user = await buildUser({
              companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
              kycStatus: KycStatusEnum.PENDING,
              activeProviders: [ProviderEnum.GOCARDLESS],
              providers: {
                stripe: {
                  id: faker.string.uuid()
                }
              }
            });

            [address, account] = await Promise.all([
              buildAddress({ owner: user._id, activeProviders: [] }),
              buildAccount({
                owner: user._id
              })
            ]);

            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should submit customer to GoCardless", async () => {
            expect(GoCardlessPaymentsService.Instance.createCustomer).toHaveBeenCalledWith({
              email: user.email,
              given_name: user.firstName,
              family_name: user.lastName,
              address_line1: address.line1,
              address_line2: address.line2,
              city: address.city,
              postal_code: address.postalCode,
              country_code: address.countryCode,
              metadata: {
                wealthyhoodId: user.id
              }
            });
            expect(GoCardlessPaymentsService.Instance.createCustomer).toBeCalledTimes(1);
          });

          it("should update our model", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser?.providers?.gocardless?.id).toEqual(GOCARDLESS_CUSTOMER_ID);
          });

          it("should submit account to Wealthkernel", async () => {
            expect(WealthkernelService.EUInstance.createAccount).toHaveBeenCalledWith({
              clientReference: account.id,
              currency: user.currency,
              name: "General Investment Account",
              owner: process.env.WEALTHYHOOD_EUROPE_WK_PARTY,
              productId: "prd-gia-corporate",
              type: "GIA"
            });
          });

          it("should update the account document with the newly created account", async () => {
            const updatedAccount = await Account.findOne({ owner: user._id });
            expect(updatedAccount?.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
          });

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });
        });

        describe("user does not have a Wealthkernel party ID in our database", () => {
          let user: UserDocument;
          let account: AccountDocument;

          let actualResponse: request.Response;

          const wkRetrievePartiesResponse = buildWealthkernelPartyResponse({
            id: process.env.WEALTHYHOOD_EUROPE_WK_PARTY
          });
          const wkCreateAccountResponse = { id: faker.string.uuid() };

          beforeAll(async () => {
            _startup();

            jest
              .spyOn(WealthkernelService.EUInstance, "retrieveParties")
              .mockResolvedValue([wkRetrievePartiesResponse]);
            jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
            jest.spyOn(WealthkernelService.EUInstance, "addAddress");
            jest.spyOn(WealthkernelService.EUInstance, "createParty");
            jest.spyOn(WealthkernelService.EUInstance, "createAccount").mockResolvedValue(wkCreateAccountResponse);

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
              providers: {
                stripe: {
                  id: faker.string.uuid()
                },
                gocardless: {
                  id: faker.string.uuid()
                }
              }
            });

            [, account] = await Promise.all([
              buildAddress({ owner: user._id, activeProviders: [] }),
              buildAccount({
                owner: user._id
              })
            ]);

            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should NOT submit party to Wealthkernel", async () => {
            expect(WealthkernelService.EUInstance.createParty).not.toHaveBeenCalled();
          });

          it("should update the user document with the Wealthyhood Europe WK party", async () => {
            const updatedUser = await User.findById(user._id);
            expect(updatedUser?.providers?.wealthkernel?.id).toEqual(process.env.WEALTHYHOOD_EUROPE_WK_PARTY);
          });

          it("should NOT submit address to Wealthkernel", async () => {
            expect(WealthkernelService.EUInstance.addAddress).not.toHaveBeenCalled();
          });

          it("should submit account to Wealthkernel", async () => {
            expect(WealthkernelService.EUInstance.createAccount).toHaveBeenCalledWith({
              clientReference: account.id,
              currency: user.currency,
              name: "General Investment Account",
              owner: process.env.WEALTHYHOOD_EUROPE_WK_PARTY,
              productId: "prd-gia-corporate",
              type: "GIA"
            });
          });

          it("should update the account document with the newly created account", async () => {
            const updatedAccount = await Account.findOne({ owner: user._id });
            expect(updatedAccount?.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
          });

          it("should return 200 with verifying status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verifying"
              })
            );
          });
        });

        describe("user does not have a Wealthkernel account ID in our database", () => {
          describe("user does not have a Wealthkernel account submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkCreateAccountResponse = { id: faker.string.uuid() };

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.EUInstance, "retrieveAccounts").mockResolvedValue([]);
              jest
                .spyOn(WealthkernelService.EUInstance, "createAccount")
                .mockResolvedValue(wkCreateAccountResponse);

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                currency: "EUR",
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  },
                  gocardless: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, activeProviders: [] });
              account = await buildAccount({
                owner: user._id
              });
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should submit account to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createAccount).toBeCalledWith({
                type: account.wrapperType,
                name: account.name,
                productId: "prd-gia-corporate",
                owner: user.providers.wealthkernel.id,
                clientReference: account.id,
                currency: "EUR"
              });
              expect(WealthkernelService.EUInstance.createAccount).toBeCalledTimes(1);
            });

            it("should not emit an event", async () => {
              expect(eventEmitter.emit).toBeCalledTimes(0);
            });

            it("should update our model", async () => {
              const updatedAccount = (await Account.findOne({
                owner: user._id
              })) as AccountDocument;
              expect(updatedAccount.providers?.wealthkernel?.id).toEqual(wkCreateAccountResponse.id);
              expect(updatedAccount.providers?.wealthkernel?.status).toEqual("Pending");
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });

          describe("user has a Wealthkernel account submitted and is pending", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse({
              status: "Pending"
            });

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "createAccount");

              user = await buildUser({
                kycStatus: KycStatusEnum.PENDING,
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                currency: "EUR",
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  },
                  gocardless: {
                    id: faker.string.uuid()
                  }
                }
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, activeProviders: [] });
              account = await buildAccount({
                owner: user._id
              });
              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id
                },
                false
              );
              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should retrieve WK accounts using party ID and client reference", async () => {
              expect(WealthkernelService.EUInstance.retrieveAccounts).toHaveBeenCalledWith({
                owner: user.providers.wealthkernel.id,
                clientReference: account.id
              });
            });

            it("should not submit account to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createAccount).toBeCalledTimes(0);
            });

            it("should not emit event", async () => {
              expect(eventEmitter.emit).not.toHaveBeenCalled();
            });

            it("should return 200 with verifying status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verifying"
                })
              );
            });
          });
        });

        describe("user has a pending account in our database but it is active in Wealthkernel", () => {
          let user: UserDocument;
          let account: AccountDocument;
          let actualResponse: request.Response;

          const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

          beforeAll(async () => {
            _startup();
            jest
              .spyOn(WealthkernelService.EUInstance, "retrieveAccounts")
              .mockResolvedValue([wkRetrieveAccountsResponse]);
            jest.spyOn(WealthkernelService.EUInstance, "createAccount");

            user = await buildUser({
              kycStatus: KycStatusEnum.PENDING,
              companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
              providers: {
                wealthkernel: { id: faker.string.uuid() },
                stripe: {
                  id: faker.string.uuid()
                },
                gocardless: {
                  id: faker.string.uuid()
                }
              }
            });
            await _mockSuccessfulSumsubPassportVerification(user);
            await buildAddress({ owner: user._id, activeProviders: [] });
            account = await buildAccount({
              owner: user._id,
              providers: {
                wealthkernel: {
                  id: wkRetrieveAccountsResponse.id,
                  status: "Pending"
                }
              }
            });
            await buildPortfolio(
              {
                owner: user._id,
                account: account._id,
                providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
              },
              false
            );
            actualResponse = await request(app)
              .post("/api/m2m/users/verify")
              .send({})
              .set("external-user-id", user._id)
              .set("Accept", "application/json");
          });
          afterAll(_teardown);

          it("should not submit account to Wealthkernel", async () => {
            expect(WealthkernelService.EUInstance.createAccount).toBeCalledTimes(0);
          });

          it("should update our account model", async () => {
            const updatedAccount = (await Account.findOne({ owner: user._id })) as AccountDocument;
            expect(updatedAccount.providers?.wealthkernel?.id).toEqual(wkRetrieveAccountsResponse.id);
            expect(updatedAccount.providers?.wealthkernel?.status).toEqual("Active");
          });

          it("should set user's kyc passed flag to true", async () => {
            const updatedUser = (await User.findById(user._id)) as UserDocument;
            expect(updatedUser.hasPassedKyc).toEqual(true);
          });

          it("should return 200 with verified status", async () => {
            expect(actualResponse.status).toEqual(200);
            expect(JSON.parse(actualResponse.text)).toMatchObject(
              expect.objectContaining({
                status: "verified"
              })
            );
          });
        });

        describe("user does not have a Wealthkernel portfolio ID in our database", () => {
          describe("user does not have a Wealthkernel portfolio submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkCreatePortfolioResponse = { id: faker.string.uuid() };
            const wkRetrieveAccountsResponse: AccountType = buildWealthkernelAccountResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountsResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "retrievePortfolios").mockResolvedValue([]);
              jest
                .spyOn(WealthkernelService.EUInstance, "createPortfolio")
                .mockResolvedValue(wkCreatePortfolioResponse);
              jest.spyOn(WealthkernelService.EUInstance, "createW8BenForm");

              user = await buildUser({
                kycStatus: KycStatusEnum.PASSED,
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  },
                  gocardless: {
                    id: faker.string.uuid()
                  }
                },
                submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1)
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, activeProviders: [] });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountsResponse.id,
                    status: "Active"
                  }
                }
              });
              await buildPortfolio({ owner: user._id, account: account._id }, false);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should NOT create W-8BEN form in Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createW8BenForm).not.toHaveBeenCalled();
            });

            it("should submit portfolio to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createPortfolio).toBeCalledWith({
                accountId: account.providers?.wealthkernel?.id,
                name: "General Investment Portfolio",
                currency: CurrencyEnum.GBP,
                mandate: {
                  type: "ExecutionOnlyMandate"
                }
              });
              expect(WealthkernelService.EUInstance.createPortfolio).toBeCalledTimes(1);
            });

            it("should emit event", async () => {
              expect(eventEmitter.emit).toBeCalledWith(
                events.user.verification.eventId,
                expect.objectContaining({ id: user.id }),
                expect.objectContaining({ emailNotification: false })
              );
            });

            it("should update our model", async () => {
              const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
              expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkCreatePortfolioResponse.id);
            });

            it("should return 200 with verified status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });

          describe("user has a Wealthkernel portfolio submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            const wkRetrieveAccountResponse: AccountType = buildWealthkernelAccountResponse();
            const wkRetrievePortfoliosResponse: PortfolioType = buildWealthkernelPortfoliosResponse();
            const wklistAdressesResponse: AddressType = buildWealthkernelAddressResponse();

            beforeAll(async () => {
              _startup();
              jest
                .spyOn(WealthkernelService.EUInstance, "retrieveAccounts")
                .mockResolvedValue([wkRetrieveAccountResponse]);
              jest
                .spyOn(WealthkernelService.EUInstance, "retrievePortfolios")
                .mockResolvedValue([wkRetrievePortfoliosResponse]);
              jest
                .spyOn(WealthkernelService.EUInstance, "listAddresses")
                .mockResolvedValue([wklistAdressesResponse]);
              jest.spyOn(WealthkernelService.EUInstance, "createW8BenForm");
              jest.spyOn(WealthkernelService.EUInstance, "createPortfolio");

              user = await buildUser({
                kycStatus: KycStatusEnum.PASSED,
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                providers: {
                  wealthkernel: { id: faker.string.uuid() },
                  stripe: {
                    id: faker.string.uuid()
                  },
                  gocardless: {
                    id: faker.string.uuid()
                  }
                },
                submittedRequiredInfoAt: DateUtil.getDateOfMinutesAgo(1)
              });
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, activeProviders: [] });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: wkRetrieveAccountResponse.id,
                    status: "Active"
                  }
                }
              });
              await buildPortfolio({ owner: user._id, account: account._id }, false);

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should NOT create W-8BEN form in Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createW8BenForm).not.toHaveBeenCalled();
            });

            it("should not submit portfolio to Wealthkernel", async () => {
              expect(WealthkernelService.EUInstance.createPortfolio).toBeCalledTimes(0);
            });

            it("should emit event", async () => {
              expect(eventEmitter.emit).toBeCalledWith(
                events.user.verification.eventId,
                expect.objectContaining({ id: user.id }),
                expect.objectContaining({ emailNotification: false })
              );
            });

            it("should sync our model", async () => {
              const updatedPortfolio = (await Portfolio.findOne({ owner: user._id })) as PortfolioDocument;
              expect(updatedPortfolio.providers?.wealthkernel?.id).toEqual(wkRetrievePortfoliosResponse.id);
            });

            it("should return 200 with verified status", async () => {
              expect(actualResponse.status).toEqual(200);
              expect(JSON.parse(actualResponse.text)).toMatchObject(
                expect.objectContaining({
                  status: "verified"
                })
              );
            });
          });

          describe("user has a Wealthkernel account ID with status 'Active' in our database but has no tax residency submitted", () => {
            let user: UserDocument;
            let account: AccountDocument;
            let actualResponse: request.Response;

            beforeAll(async () => {
              _startup();
              jest.spyOn(WealthkernelService.UKInstance, "retrieveAccounts");
              jest.spyOn(WealthkernelService.UKInstance, "createAccount");
              jest.spyOn(WealthkernelService.UKInstance, "createPortfolio");

              jest.spyOn(environmentUtil, "envIsProd").mockReturnValue(true);

              await buildUser({
                residencyCountry: "GR",
                companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                taxResidency: undefined
              });
              user = await buildUser(
                {
                  kycStatus: KycStatusEnum.PENDING,
                  w8BenForm: {
                    activeProviders: [ProviderEnum.WEALTHKERNEL],
                    completedAt: new Date(),
                    providers: {
                      wealthkernel: {
                        id: faker.string.uuid(),
                        status: "Completed"
                      }
                    }
                  },
                  providers: {
                    wealthkernel: { id: faker.string.uuid() },
                    stripe: {
                      id: faker.string.uuid()
                    }
                  },
                  residencyCountry: "GR",
                  companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
                  taxResidency: undefined
                },
                false
              );
              await _mockSuccessfulSumsubPassportVerification(user);
              await buildAddress({ owner: user._id, providers: { wealthkernel: { id: faker.string.uuid() } } });
              account = await buildAccount({
                owner: user._id,
                providers: {
                  wealthkernel: {
                    id: faker.string.uuid(),
                    status: "Active"
                  }
                }
              });
              await buildBankAccount({
                owner: user._id,
                providers: { wealthkernel: { id: faker.string.uuid() } }
              });
              await user.populate("bankAccounts");

              await buildPortfolio(
                {
                  owner: user._id,
                  account: account._id,
                  providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
                },
                false
              );

              actualResponse = await request(app)
                .post("/api/m2m/users/verify")
                .send({})
                .set("external-user-id", user._id)
                .set("Accept", "application/json");
            });
            afterAll(_teardown);

            it("should return 400 because taxResidency is required", async () => {
              expect(actualResponse.status).toEqual(400);
            });
          });
        });
      });
    });
  });

  describe("POST /users/:id", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should succeed to update user with status 200 returning object containing updated user", async () => {
      const user = await buildUser({
        isUKTaxResident: true,
        dateOfBirth: new Date("1981-02-12"),
        viewedWelcomePage: false
      });

      const userUpdateData: Partial<UserDocument> = {
        firstName: "Paris",
        lastName: "Kolovos",
        isUKTaxResident: false,
        dateOfBirth: new Date("1994-05-11T00:00:00.000Z"),
        nationalities: ["GR"],
        taxResidency: {
          countryCode: "GB",
          proofType: "NINO",
          value: "test"
        },
        viewedWelcomePage: true
      };

      const response = await request(app)
        .post(`/api/m2m/users/${user._id}`)
        .set("external-user-id", user._id)
        .send(userUpdateData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      // reparse objects to resolve dates fields format problem
      const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
      Object.entries(JSON.parse(JSON.stringify(userUpdateData))).forEach(([key, value]) =>
        expect(updatedUser[key]).toEqual(value)
      );
    });

    describe("and tries to update user's 'viewedWelcomePage' to true for the first time", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const userUpdateData: Partial<UserDocument> = {
        viewedWelcomePage: true
      };

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false
        });

        jest.spyOn(eventEmitter, "emit");
        response = await request(app)
          .post(`/api/m2m/users/${user._id}`)
          .set("external-user-id", user._id)
          .send({
            viewedWelcomePage: userUpdateData.viewedWelcomePage
          })
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        // reparse object to resolve dates fields format problem
        const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
        Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
      });
      it("should emit a 'welcome' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.welcome.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("and tries to update user's 'viewedWelcomePage' while it was already true", () => {
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: true
        });

        jest.spyOn(eventEmitter, "emit");
      });
      [true, false].forEach((viewedWelcomePage) => {
        it("should NOT emit a 'welcome' event", async () => {
          await request(app)
            .post(`/api/m2m/users/${user._id}`)
            .set("external-user-id", user._id)
            .send({
              viewedWelcomePage
            })
            .set("Accept", "application/json");

          expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        });
      });
    });

    describe("and tries to update user's 'viewedKYCSuccessPage' to true", () => {
      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: true,
          viewedKYCSuccessPage: false
        });

        jest.spyOn(eventEmitter, "emit");
        response = await request(app)
          .post(`/api/m2m/users/${user._id}`)
          .set("external-user-id", user._id)
          .send({
            viewedKYCSuccessPage: true
          })
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        expect(JSON.parse(response.text)).toMatchObject({
          data: [
            {
              viewedKYCSuccessPage: true,
              shouldShowKYCSuccessPage: false
            }
          ]
        });
      });
    });

    describe("and tries to update user's taxResidency", () => {
      let response: supertest.Response;
      let user: UserDocument;
      let userUpdateData: Partial<UserDocument>;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: false,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          taxResidency: null
        });

        jest.spyOn(eventEmitter, "emit");
      });
      describe("and userUpdateData contains all necessary fields for event emissions", () => {
        beforeAll(async () => {
          _startup();
          userUpdateData = {
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "test"
            },
            isUKTaxResident: true
          };

          response = await request(app)
            .post(`/api/m2m/users/${user._id}`)
            .set("external-user-id", user._id)
            .send(userUpdateData)
            .set("Accept", "application/json");
        });

        it("should succeed to update user with status 200 returning object containing updated user", async () => {
          expect(response.status).toEqual(200);
          // reparse object to resolve dates fields format problem
          const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
          Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
        });
        it("should emit a 'taxDetailsSubmission' event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.user.taxDetailsSubmission.eventId,
            expect.objectContaining({ id: user.id })
          );
        });
      });

      describe("and tries to update user's 'viewedWelcomePage' while it was already true", () => {
        let user: UserDocument;

        beforeAll(async () => {
          _startup();
          user = await buildUser({
            nationalities: ["AL"],
            isUKTaxResident: true,
            dateOfBirth: new Date("1981-02-12"),
            viewedWelcomePage: true
          });

          jest.spyOn(eventEmitter, "emit");
        });
        [true, false].forEach((viewedWelcomePage) => {
          it("should NOT emit a 'welcome' event", async () => {
            await request(app)
              .post(`/api/m2m/users/${user._id}`)
              .set("external-user-id", user._id)
              .send({
                viewedWelcomePage
              })
              .set("Accept", "application/json");

            expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
          });
        });
      });

      describe("and tries to update user's taxResidency", () => {
        let response: supertest.Response;
        let user: UserDocument;
        let userUpdateData: Partial<UserDocument>;

        beforeAll(async () => {
          _startup();
          user = await buildUser({
            nationalities: ["AL"],
            isUKTaxResident: false,
            dateOfBirth: new Date("1981-02-12"),
            viewedWelcomePage: false,
            taxResidency: null
          });

          jest.spyOn(eventEmitter, "emit");
        });
        describe("and userUpdateData contains all necessary fields for event emissions", () => {
          beforeAll(async () => {
            _startup();
            userUpdateData = {
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: "test"
              },
              isUKTaxResident: true
            };

            const { taxResidency, isUKTaxResident } = userUpdateData;

            response = await request(app)
              .post(`/api/m2m/users/${user._id}`)
              .set("external-user-id", user._id)
              .send({
                taxResidency,
                isUKTaxResident,
                submittedRequiredInfoAt: new Date()
              })
              .set("Accept", "application/json");
          });

          it("should succeed to update user with status 200 returning object containing updated user", async () => {
            expect(response.status).toEqual(200);
            // reparse object to resolve dates fields format problem
            const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
            Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
          });
          it("should emit a 'taxDetailsSubmission' event", async () => {
            expect(eventEmitter.emit).toHaveBeenCalledWith(
              events.user.taxDetailsSubmission.eventId,
              expect.objectContaining({ id: user.id })
            );
          });
          it("should not emit a 'personalDetailsSubmission' event", async () => {
            expect(eventEmitter.emit).not.toHaveBeenCalledWith(
              events.user.personalDetailsSubmission.eventId,
              expect.objectContaining({ id: user.id })
            );
          });
        });
        describe("and userUpdateData does NOT contains all necessary fields for event emissions", () => {
          beforeAll(async () => {
            _startup();
            userUpdateData = {
              taxResidency: {
                countryCode: "GB",
                proofType: "NINO",
                value: "test"
              }
            };

            const { taxResidency } = userUpdateData;

            response = await request(app)
              .post(`/api/m2m/users/${user._id}`)
              .set("external-user-id", user._id)
              .send({
                taxResidency,
                submittedRequiredInfoAt: new Date()
              })
              .set("Accept", "application/json");
          });

          it("should succeed to update user with status 200 returning object containing updated user", async () => {
            expect(response.status).toEqual(200);
            // reparse object to resolve dates fields format problem
            const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
            Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
          });
          it("should NOT emit any event", async () => {
            expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
          });
        });
      });
      describe("and userUpdateData does NOT contains all necessary fields for event emissions", () => {
        beforeAll(async () => {
          _startup();
          userUpdateData = {
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "test"
            }
          };

          response = await request(app)
            .post(`/api/m2m/users/${user._id}`)
            .set("external-user-id", user._id)
            .send(userUpdateData)
            .set("Accept", "application/json");
        });

        it("should succeed to update user with status 200 returning object containing updated user", async () => {
          expect(response.status).toEqual(200);
          // reparse object to resolve dates fields format problem
          const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
          Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
        });
        it("should NOT emit any event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        });
      });

      ["countryCode", "proofType", "value"].forEach((key) =>
        it("should return status 400 if 'taxResidency' props 'countryCode', 'proofType', 'value' params don't coexist", async () => {
          const user = await buildUser();
          const response = await request(app)
            .post(`/api/m2m/users/${user._id}`)
            .set("external-user-id", user._id)
            .send({ taxResidency: { [key]: faker.string.sample() } })
            .set("Accept", "application/json");
          expect(response.status).toEqual(400);
          expect(JSON.parse(response.text)).toMatchObject(
            expect.objectContaining({
              error: {
                description: "Invalid parameter",
                message:
                  "Invalid parameter 'taxResidency' properties 'countryCode', 'proofType', 'value' must coexist"
              }
            })
          );
        })
      );

      ["dateOfBirth", "nationalities", "taxResidency", "isUKTaxResident", "viewedWelcomePage"].forEach((key) =>
        it(`should return status 400 for invalid parameter '${key}'`, async () => {
          const user = await buildUser();
          const response = await request(app)
            .post(`/api/m2m/users/${user._id}`)
            .set("external-user-id", user._id)
            .send({ [key]: faker.string.uuid() })
            .set("Accept", "application/json");
          expect(response.status).toEqual(400);
        })
      );

      it("should return status 400 for invalid parameter 'nationalities'", async () => {
        const user = await buildUser();
        const response = await request(app)
          .post(`/api/m2m/users/${user._id}`)
          .set("external-user-id", user._id)
          .send({ nationalities: [faker.string.sample()] })
          .set("Accept", "application/json");
        expect(response.status).toEqual(400);
      });
    });
  });

  describe("POST /users/me", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should succeed to update user with status 200 returning object containing updated user", async () => {
      const user = await buildUser({
        isUKTaxResident: true,
        dateOfBirth: new Date("1981-02-12"),
        viewedWelcomePage: false
      });

      const userUpdateData: Partial<UserDocument> = {
        firstName: "Paris",
        lastName: "Kolovos",
        isUKTaxResident: false,
        dateOfBirth: new Date("1994-05-11T00:00:00.000Z"),
        nationalities: ["GR"],
        taxResidency: {
          countryCode: "GB",
          proofType: "NINO",
          value: "test"
        },
        viewedWelcomePage: true
      };

      const response = await request(app)
        .post("/api/m2m/users/me")
        .set("external-user-id", user._id)
        .send(userUpdateData)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      // reparse objects to resolve dates fields format problem
      const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
      Object.entries(JSON.parse(JSON.stringify(userUpdateData))).forEach(([key, value]) =>
        expect(updatedUser[key]).toEqual(value)
      );
    });

    describe("and tries to update user's 'viewedWelcomePage' to true for the first time", () => {
      let response: supertest.Response;
      let user: UserDocument;
      const userUpdateData: Partial<UserDocument> = {
        viewedWelcomePage: true
      };

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false
        });

        jest.spyOn(eventEmitter, "emit");
        response = await request(app)
          .post("/api/m2m/users/me")
          .set("external-user-id", user._id)
          .send({
            viewedWelcomePage: userUpdateData.viewedWelcomePage
          })
          .set("Accept", "application/json");
      });

      it("should succeed to update user with status 200 returning object containing updated user", async () => {
        expect(response.status).toEqual(200);
        // reparse object to resolve dates fields format problem
        const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
        Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
      });

      it("should emit a 'welcome' event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.welcome.eventId,
          expect.objectContaining({ id: user.id })
        );
      });
    });

    describe("and tries to update user's 'viewedWelcomePage' while it was already true", () => {
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: true
        });

        jest.spyOn(eventEmitter, "emit");
      });

      [true, false].forEach((viewedWelcomePage) => {
        it("should NOT emit a 'welcome' event", async () => {
          await request(app)
            .post("/api/m2m/users/me")
            .set("external-user-id", user._id)
            .send({
              viewedWelcomePage
            })
            .set("Accept", "application/json");

          expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        });
      });
    });

    describe("and tries to update user's 'skippedPortfolioCreation' for the first time", () => {
      let user: UserDocument;

      beforeEach(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12")
        });

        jest.spyOn(eventEmitter, "emit");
      });

      it("should update the user document when value is 'true'", async () => {
        const skippedPortfolioCreation = true;

        await request(app)
          .post("/api/m2m/users/me")
          .set("external-user-id", user._id)
          .send({
            skippedPortfolioCreation
          })
          .set("Accept", "application/json");

        expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.skippedPortfolioCreation).toEqual(skippedPortfolioCreation);
      });

      it("should update the user document when value is 'false'", async () => {
        const skippedPortfolioCreation = false;

        await request(app)
          .post("/api/m2m/users/me")
          .set("external-user-id", user._id)
          .send({
            skippedPortfolioCreation
          })
          .set("Accept", "application/json");

        expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        const updatedUser = await User.findById(user.id);
        expect(updatedUser?.skippedPortfolioCreation).toEqual(skippedPortfolioCreation);
      });
    });

    describe("and tries to update user's taxResidency", () => {
      let response: supertest.Response;
      let user: UserDocument;
      let userUpdateData: Partial<UserDocument>;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: false,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: false,
          taxResidency: null
        });

        jest.spyOn(eventEmitter, "emit");
      });

      describe("and userUpdateData contains all necessary fields for event emissions", () => {
        beforeAll(async () => {
          _startup();
          userUpdateData = {
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "test"
            },
            isUKTaxResident: true
          };

          response = await request(app)
            .post("/api/m2m/users/me")
            .set("external-user-id", user._id)
            .send(userUpdateData)
            .set("Accept", "application/json");
        });

        it("should succeed to update user with status 200 returning object containing updated user", async () => {
          expect(response.status).toEqual(200);
          // reparse object to resolve dates fields format problem
          const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
          Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
        });

        it("should emit a 'taxDetailsSubmission' event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.user.taxDetailsSubmission.eventId,
            expect.objectContaining({ id: user.id })
          );
        });

        it("should not emit a 'personalDetailsSubmission' event", async () => {
          expect(eventEmitter.emit).not.toHaveBeenCalledWith(
            events.user.personalDetailsSubmission.eventId,
            expect.objectContaining({ id: user.id })
          );
        });
      });

      describe("and userUpdateData does NOT contains all necessary fields for event emissions", () => {
        beforeAll(async () => {
          _startup();
          userUpdateData = {
            taxResidency: {
              countryCode: "GB",
              proofType: "NINO",
              value: "test"
            }
          };

          response = await request(app)
            .post("/api/m2m/users/me")
            .set("external-user-id", user._id)
            .send(userUpdateData)
            .set("Accept", "application/json");
        });

        it("should succeed to update user with status 200 returning object containing updated user", async () => {
          expect(response.status).toEqual(200);
          // reparse object to resolve dates fields format problem
          const updatedUser: any = JSON.parse(JSON.stringify(await User.findById(user.id)));
          Object.entries(userUpdateData).forEach(([key, value]) => expect(updatedUser[key]).toEqual(value));
        });
        it("should NOT emit any event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        });
      });
    });

    describe("and tries to update user's 'viewedWelcomePage' while it was already true", () => {
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser({
          nationalities: ["AL"],
          isUKTaxResident: true,
          dateOfBirth: new Date("1981-02-12"),
          viewedWelcomePage: true
        });

        jest.spyOn(eventEmitter, "emit");
      });

      [true, false].forEach((viewedWelcomePage) => {
        it("should NOT emit a 'welcome' event", async () => {
          await request(app)
            .post("/api/m2m/users/me")
            .set("external-user-id", user._id)
            .send({
              viewedWelcomePage
            })
            .set("Accept", "application/json");

          expect(eventEmitter.emit).toHaveBeenCalledTimes(0);
        });
      });
    });

    ["countryCode", "proofType", "value"].forEach((key) =>
      it("should return status 400 if 'taxResidency' props 'countryCode', 'proofType', 'value' params don't coexist", async () => {
        const user = await buildUser();
        const response = await request(app)
          .post("/api/m2m/users/me")
          .set("external-user-id", user._id)
          .send({ taxResidency: { [key]: faker.string.sample() } })
          .set("Accept", "application/json");
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message:
                "Invalid parameter 'taxResidency' properties 'countryCode', 'proofType', 'value' must coexist"
            }
          })
        );
      })
    );

    ["dateOfBirth", "nationalities", "taxResidency", "isUKTaxResident", "viewedWelcomePage"].forEach((key) =>
      it(`should return status 400 for invalid parameter '${key}'`, async () => {
        const user = await buildUser();
        const response = await request(app)
          .post("/api/m2m/users/me")
          .set("external-user-id", user._id)
          .send({ [key]: faker.string.uuid() })
          .set("Accept", "application/json");
        expect(response.status).toEqual(400);
      })
    );

    it("should return status 400 for invalid parameter 'nationalities'", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me")
        .set("external-user-id", user._id)
        .send({ nationalities: [faker.string.sample()] })
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });
  });

  describe("POST /users/me/join-waiting-list", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    describe("when attempting to access the endpoint without authentication", () => {
      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/join-waiting-list")
          .send({})
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("when attempting to join the waiting list without setting a residency country", () => {
      let user: UserDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.resetAllMocks();
        user = await buildUser();

        response = await request(app)
          .post("/api/m2m/users/me/join-waiting-list")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return status 400", () => {
        expect(response.status).toEqual(400);
      });

      it("should NOT update user with joinedWaitingListAt timestamp", async () => {
        const updatedUser = await User.findById(user._id);
        expect(updatedUser?.joinedWaitingListAt).not.toBeTruthy();
      });
    });

    describe("when an authenticated user joins the waiting list for the first time", () => {
      let user: UserDocument;
      let response: supertest.Response;

      beforeAll(async () => {
        jest.resetAllMocks();
        user = await buildUser();

        response = await request(app)
          .post("/api/m2m/users/me/join-waiting-list")
          .send({ residencyCountry: "GR" })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return status 200", () => {
        expect(response.status).toEqual(200);
      });

      it("should update user with joinedWaitingListAt timestamp", async () => {
        const updatedUser = await User.findById(user._id);
        expect(updatedUser?.joinedWaitingListAt).toBeTruthy();
        expect(updatedUser?.joinedWaitingListAt instanceof Date).toBe(true);
      });

      it("should update user with residency country", async () => {
        const updatedUser = await User.findById(user._id);
        expect(updatedUser?.residencyCountry).toBe("GR");
      });
    });

    describe("when a user who has already joined the waiting list attempts to join again", () => {
      let user: UserDocument;
      let firstResponse: supertest.Response;
      let secondResponse: supertest.Response;
      let firstTimestamp: Date;
      let secondTimestamp: Date;

      beforeAll(async () => {
        jest.resetAllMocks();
        user = await buildUser({ residencyCountry: "GR" });

        // First request to join waiting list
        firstResponse = await request(app)
          .post("/api/m2m/users/me/join-waiting-list")
          .send({ residencyCountry: "GR" })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const userAfterFirstJoin = await User.findById(user._id);
        firstTimestamp = userAfterFirstJoin?.joinedWaitingListAt;

        // Wait a short time to ensure timestamps would be different
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Second request to join waiting list
        secondResponse = await request(app)
          .post("/api/m2m/users/me/join-waiting-list")
          .send({ residencyCountry: "GR" })
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        const userAfterSecondJoin = await User.findById(user._id);
        secondTimestamp = userAfterSecondJoin?.joinedWaitingListAt;
      });
      afterAll(_teardown);

      it("should return status 200 for both requests", () => {
        expect(firstResponse.status).toEqual(200);
        expect(secondResponse.status).toEqual(200);
      });

      it("should update the timestamp on subsequent requests", () => {
        expect(firstTimestamp).toBeTruthy();
        expect(secondTimestamp).toBeTruthy();
        expect(secondTimestamp.getTime()).toBeGreaterThan(firstTimestamp.getTime());
      });
    });
  });

  describe("POST /users/me/set-referrer", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    describe("when user sets non-existing referral code", () => {
      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser();

        response = await request(app)
          .post("/api/m2m/users/me/set-referrer")
          .set("external-user-id", user._id)
          .send({
            referralCode: faker.string.sample(8)
          })
          .set("Accept", "application/json");
      });

      it("should throw a 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "Referral code does not exist!",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when user tries to refer themselves", () => {
      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser();
        const referralCode = await buildReferralCode({ owner: user.id });

        response = await request(app)
          .post("/api/m2m/users/me/set-referrer")
          .set("external-user-id", user._id)
          .send({
            referralCode: referralCode.code
          })
          .set("Accept", "application/json");
      });

      it("should throw a 400", async () => {
        expect(response.status).toEqual(400);
        expect(JSON.parse(response.text)).toMatchObject(
          expect.objectContaining({
            error: {
              message: "User tried to refer themselves!",
              description: "Operation failed"
            }
          })
        );
      });
    });

    describe("when user is already referred", () => {
      let response: supertest.Response;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        const anotherUser = await buildUser();
        user = await buildUser({
          referredByEmail: anotherUser.email
        });

        const referralCode = await buildReferralCode({ owner: anotherUser.id });

        response = await request(app)
          .post("/api/m2m/users/me/set-referrer")
          .set("external-user-id", user._id)
          .send({
            referralCode: referralCode.code
          })
          .set("Accept", "application/json");
      });

      it("should return a 204", async () => {
        expect(response.status).toEqual(204);
      });
    });

    describe("when user uses a valid expiring referral code", () => {
      let response: supertest.Response;
      let usedReferralCode: ReferralCodeDocument;
      let anotherUser: UserDocument;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser();
        anotherUser = await buildUser();
        buildParticipant({ email: anotherUser.email, owner: anotherUser.id });
        usedReferralCode = await buildReferralCode({ owner: anotherUser.id, lifetime: LifetimeEnum.EXPIRING });

        const existingReferralCodes = await ReferralCode.find({ owner: anotherUser.id });
        expect(existingReferralCodes.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/users/me/set-referrer")
          .set("external-user-id", user._id)
          .send({
            referralCode: usedReferralCode.code
          })
          .set("Accept", "application/json");
      });

      it("should respond with a 204 status", async () => {
        expect(response.status).toEqual(204);
      });
    });

    describe("when user uses a valid non-expiring referral code", () => {
      let response: supertest.Response;
      let usedReferralCode: ReferralCodeDocument;
      let anotherUser: UserDocument;
      let user: UserDocument;

      beforeAll(async () => {
        _startup();
        user = await buildUser();
        anotherUser = await buildUser();
        buildParticipant({ email: anotherUser.email, owner: anotherUser.id });
        usedReferralCode = await buildReferralCode({ owner: anotherUser.id, lifetime: LifetimeEnum.NON_EXPIRING });

        const existingReferralCodes = await ReferralCode.find({ owner: anotherUser.id });
        expect(existingReferralCodes.length).toBe(1);

        response = await request(app)
          .post("/api/m2m/users/me/set-referrer")
          .set("external-user-id", user._id)
          .send({
            referralCode: usedReferralCode.code
          })
          .set("Accept", "application/json");
      });

      it("should respond with a 204 status", async () => {
        expect(response.status).toEqual(204);
      });
    });
  });

  describe("POST /users/me/residency-country", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should fail with status 400 if the residency country is empty", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the residency country has forbidden value", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry: "no-country" })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should update the user document with the residency country, active providers, currency & company entity when residency country is GB", async () => {
      const residencyCountry = "GB";

      const user = await buildUser({ residencyCountry: undefined });
      expect(user.residencyCountry).toBeUndefined();
      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser.activeProviders.length).toBe(3);
      expect(updatedUser).toEqual(
        expect.objectContaining({
          residencyCountry,
          currency: "GBP",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          activeProviders: expect.arrayContaining([
            ProviderEnum.WEALTHKERNEL,
            ProviderEnum.STRIPE,
            ProviderEnum.SUMSUB
          ]),
          w8BenForm: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        })
      );

      const paymentAccounts = await Wallet.find({ owner: user.id });
      expect(paymentAccounts.length).toBe(0);
    });

    it("should update the user document with the residency country, active providers, currency & company entity and create a wallet when residency country is in EU", async () => {
      const residencyCountry = "GR";

      const user = await buildUser({ residencyCountry: undefined });
      expect(user.residencyCountry).toBeUndefined();
      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);
      const updatedUser = await User.findOne({ _id: user.id });

      expect(updatedUser.activeProviders.length).toBe(4);
      expect(updatedUser).toEqual(
        expect.objectContaining({
          residencyCountry,
          currency: "EUR",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          activeProviders: expect.arrayContaining([
            ProviderEnum.WEALTHKERNEL,
            ProviderEnum.STRIPE,
            ProviderEnum.SUMSUB,
            ProviderEnum.GOCARDLESS
          ]),
          w8BenForm: {
            activeProviders: []
          }
        })
      );

      const wallet = await Wallet.findOne({ owner: user.id });
      expect(wallet).toEqual(
        expect.objectContaining({
          activeProviders: [ProviderEnum.DEVENGO]
        })
      );
    });

    it("should update any existing gifts for that target user based on their residency country", async () => {
      const residencyCountry = "GB";

      const user = await buildUser({ residencyCountry: undefined });

      // As gift was sent by EU user, it has a consideration currency of EUR by default
      const gift = await buildGift({
        consideration: {
          amount: 2000,
          currency: "EUR"
        },
        targetUserEmail: user.email,
        deposit: undefined
      });

      expect(user.residencyCountry).toBeUndefined();

      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedGift = await Gift.findById(gift.id);
      expect(updatedGift).toEqual(
        expect.objectContaining({
          consideration: {
            amount: 2000,
            currency: "GBP"
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL]
          }
        })
      );
    });

    it("should add active providers to account and portfolios", async () => {
      const residencyCountry = "GB";

      const user = await buildUser({ residencyCountry: undefined });
      const account = await buildAccount({
        activeProviders: undefined,
        owner: user._id
      });
      await Promise.all([
        buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        })
      ]);

      expect(user.residencyCountry).toBeUndefined();

      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const portfolios = await Portfolio.find({ mode: PortfolioModeEnum.REAL });
      const realPortfolio = portfolios[0];
      expect(realPortfolio.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);

      const updatedAccount = await Account.findById(account._id);
      expect(updatedAccount.activeProviders).toEqual([ProviderEnum.WEALTHKERNEL]);
    });

    it("should update the currency of the user portfolio", async () => {
      const residencyCountry = "GR";

      const user = await buildUser({ residencyCountry: undefined });
      const account = await buildAccount({
        activeProviders: undefined,
        owner: user._id
      });
      const portfolio = await buildPortfolio({
        activeProviders: undefined,
        currency: "GBP",
        mode: PortfolioModeEnum.REAL,
        owner: user._id,
        account: account._id
      });

      expect(user.residencyCountry).toBeUndefined();

      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedPortfolio = await Portfolio.findById(portfolio.id);
      expect(updatedPortfolio.currency).toEqual("EUR");
    });

    it("should emit a residency country change event", async () => {
      const residencyCountry = "GB";

      const user = await buildUser({ residencyCountry: undefined });
      const account = await buildAccount({
        activeProviders: undefined,
        owner: user._id
      });
      await Promise.all([
        buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        })
      ]);

      expect(user.residencyCountry).toBeUndefined();

      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.residencyCountryChange.eventId,
        expect.objectContaining({ email: user.email })
      );
    });

    it("should emit a got whitelisted event if the user is EU Whitelisted", async () => {
      const residencyCountry = "GR";

      const user = await buildUser({
        residencyCountry: undefined,
        email: whitelistConfig.WHITELISTED_EU_EMAILS[0]
      });
      const account = await buildAccount({
        activeProviders: undefined,
        owner: user._id
      });
      await Promise.all([
        buildPortfolio({
          activeProviders: undefined,
          mode: PortfolioModeEnum.REAL,
          owner: user._id,
          account: account._id
        })
      ]);

      expect(user.residencyCountry).toBeUndefined();

      const response = await request(app)
        .post("/api/m2m/users/me/residency-country")
        .send({ residencyCountry })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      expect(eventEmitter.emit).toHaveBeenCalledWith(
        events.user.gotWhitelisted.eventId,
        expect.objectContaining({ email: user.email })
      );
    });
  });

  describe("POST /users/me/deletion-feedback", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should fail if the feedback is empty", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/deletion-feedback")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should submit the deletion feedback and emit the corresponding event", async () => {
      const feedback = faker.string.sample();

      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/deletion-feedback")
        .send({ feedback })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.deletionFeedback).toBe(feedback);

      expect(eventEmitter.emit).toBeCalledWith(
        events.user.deletionFeedbackSubmission.eventId,
        expect.objectContaining({ id: user.id })
      );
    });
  });

  describe("POST /users/prompts/seen", () => {
    let user: UserDocument;
    let firstReward: RewardDocument;
    let secondReward: RewardDocument;

    beforeAll(async () => {
      user = await buildUser();
      firstReward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });
      secondReward = await buildReward({
        hasViewedAppModal: false,
        targetUser: user.id
      });
    });

    afterAll(async () => await clearDb());

    it("POST /users/prompts/seen should return 200 and update reward document", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          modalType: "Reward",
          promptType: "modal",
          ids: [firstReward.id]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
    });

    it("POST /users/prompts/seen should return 200 and update multiple reward documents", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          modalType: "Reward",
          promptType: "modal",
          ids: [firstReward.id, secondReward.id]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
    });

    it("POST /users/prompts/seen should return 400 when requesting with invalid promptType", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          promptType: "banner"
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("POST /users/prompts/seen should return 400 when requesting with invalid modalType", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          promptType: "banner",
          modalType: "Reward"
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    it("POST /users/prompts/seen should return 400 when requesting with undefined ids field", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          promptType: "modal",
          modalType: "Reward"
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });

    // TODO: remove this test
    it("POST /users/prompts/seen should return update the reward documents of the user when requesting with undefined ids field", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          promptType: "modal",
          modalType: "Reward",
          ids: []
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      const reward1 = await Reward.findById(firstReward.id);
      const reward2 = await Reward.findById(secondReward.id);
      expect(reward1?.hasViewedAppModal).toBe(true);
      expect(reward2?.hasViewedAppModal).toBe(true);
      expect(response.status).toEqual(200);
    });

    // TODO add back this test
    it.skip("POST /users/prompts/seen should return 400 when requesting with empty ids array", async () => {
      const response = await request(app)
        .post("/api/m2m/users/prompts/seen")
        .send({
          promptType: "modal",
          modalType: "Reward",
          ids: []
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
    });
  });

  describe("POST /users/me/viewed-referral-code-screen", () => {
    let user: UserDocument;
    beforeAll(_startup);
    beforeEach(async () => {
      user = await buildUser({ viewedReferralCodeScreen: false });
    });
    afterEach(_teardown);
    describe("user ID header is missing", () => {
      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/viewed-referral-code-screen")
          .send({})
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("user ID header is not ObjectID", () => {
      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/viewed-referral-code-screen")
          .send({})
          .set("external-user-id", "invalid-user-id")
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("and the user id is valid", () => {
      it("should return 204 with viewedReferralCodeScreen set to true", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/viewed-referral-code-screen")
          .send({})
          .set("external-user-id", user._id)
          .set("Accept", "application/json");
        const updatedUser = await User.findById(user._id);

        expect(actualResponse.status).toEqual(204);
        expect(updatedUser?.viewedReferralCodeScreen).toEqual(true);
      });
    });
  });

  describe("POST /users/me/employment-info", () => {
    beforeAll(_startup);
    afterAll(_teardown);

    it("should fail with status 400 if the no data is sent", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'employmentStatus' field has invalid value", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          incomeRangeId: "1",
          employmentStatus: "invalid",
          industry: "Tobacco",
          sourcesOfWealth: ["Gift"]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'industry' field has invalid value", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          incomeRangeId: "1",
          employmentStatus: "FullTime",
          industry: "invalid",
          sourcesOfWealth: ["Gift"]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'sourcesOfWealth' field has invalid value", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          incomeRangeId: "1",
          employmentStatus: "FullTime",
          industry: "Tobacco",
          sourcesOfWealth: "Gift"
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'incomeRangeId' field is missing", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          employmentStatus: "FullTime",
          industry: "Tobacco",
          sourcesOfWealth: ["Gift"]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'incomeRangeId' field is invalid", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          incomeRangeId: "xxx",
          employmentStatus: "FullTime",
          industry: "Tobacco",
          sourcesOfWealth: ["Gift"]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should fail with status 400 if the 'industry' field is missing but the 'employmentStatus' requires it", async () => {
      const user = await buildUser();
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({
          incomeRangeId: "1",
          employmentStatus: "FullTime",
          sourcesOfWealth: ["Gift"]
        })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
    });

    it("should return status 204 status and update employemntInfo", async () => {
      const employmentInfoDTO = {
        incomeRangeId: "1",
        employmentStatus: "FullTime",
        industry: "Tobacco",
        sourcesOfWealth: ["Gift"]
      };

      const user = await buildUser({ employmentInfo: undefined });
      expect(user.employmentInfo).toEqual({ sourcesOfWealth: [] });
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({ ...employmentInfoDTO })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.employmentInfo).toMatchObject({
        ...employmentInfoDTO,
        annualIncome: { amount: 5000, currency: "GBP" }
      });
    });

    it("should return status 204 status and update employemntInfo when the 'industry' field is missing but with valid 'employedStatus'", async () => {
      const employmentInfoDTO = {
        incomeRangeId: "1",
        employmentStatus: "Retired",
        sourcesOfWealth: ["Gift"]
      };

      const user = await buildUser({ employmentInfo: undefined });
      expect(user.employmentInfo).toEqual({ sourcesOfWealth: [] });
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({ ...employmentInfoDTO })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.employmentInfo).toMatchObject({
        ...employmentInfoDTO,
        annualIncome: { amount: 5000, currency: "GBP" }
      });
    });

    it("should return status 204 status and update employemntInfo when the 'industry' field is empty string but with valid 'employedStatus'", async () => {
      const employmentInfoDTO = {
        incomeRangeId: "1",
        employmentStatus: "Retired",
        sourcesOfWealth: ["Gift"],
        industry: ""
      };

      const user = await buildUser({ employmentInfo: undefined });
      expect(user.employmentInfo).toEqual({ sourcesOfWealth: [] });
      const response = await request(app)
        .post("/api/m2m/users/me/employment-info")
        .send({ ...employmentInfoDTO })
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(204);

      const updatedUser = await User.findOne({ _id: user.id });
      expect(updatedUser?.employmentInfo).toMatchObject({
        ...employmentInfoDTO,
        annualIncome: { amount: 5000, currency: "GBP" },
        // it should not write the emptry strng to the DB
        industry: undefined
      });
    });
  });

  describe("POST /users/me/subscribe-wealthybites", () => {
    describe("user ID header is missing", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });
      afterAll(_teardown);

      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites")
          .send({})
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("user ID header is not ObjectID", () => {
      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
      });
      afterAll(_teardown);

      it("should return 401 with user not found error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites")
          .send({})
          .set("external-user-id", "invalid-user-id")
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(401);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: { description: "User not found", message: "invalid_token" }
          })
        );
      });
    });

    describe("didSubscribe is missing", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        user = await buildUser({ viewedWealthybitesScreen: false });
      });
      afterAll(_teardown);

      it("should return 400 error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(400);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Param 'didSubscribe' is required"
            }
          })
        );
      });
    });

    describe("didSubscribe is not boolean", () => {
      let user: UserDocument;

      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        user = await buildUser();
      });
      afterAll(_teardown);

      it("should return 400 error", async () => {
        const actualResponse = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites?didSubscribe=xxx")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");

        expect(actualResponse.status).toEqual(400);
        expect(JSON.parse(actualResponse.text)).toMatchObject(
          expect.objectContaining({
            error: {
              description: "Invalid parameter",
              message: "Invalid value for param 'didSubscribe' , should be boolean"
            }
          })
        );
      });
    });

    describe("request is valid and user did subscribe", () => {
      let user: UserDocument;
      let response: supertest.Response;
      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        user = await buildUser();

        await buildNotificationSettings({ owner: user.id });

        response = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites?didSubscribe=true")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return status 204 status", () => {
        expect(response.status).toEqual(204);
      });

      it("should update viewedReferralCodeScreen to true", async () => {
        const updatedUser = await User.findById(user._id);
        expect(updatedUser?.viewedWealthybitesScreen).toEqual(true);
      });

      it("should update the email notification settings of the user", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: user._id });
        expect(updatedNotificationSettings.email.settings.get(EmailNotificationSettingEnum.WEALTHYBITES)).toEqual(
          true
        );
      });

      it("should emit event that user subscribed to wealthybites", () => {
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.user.wealthybitesSubscription.eventId,
          expect.objectContaining({
            id: user.id
          }),
          { enabled: true }
        );
      });
    });

    describe("request is valid and user did not subscribe", () => {
      let user: UserDocument;
      let response: supertest.Response;
      beforeAll(async () => {
        jest.resetAllMocks();
        await clearDb();
        user = await buildUser({ viewedWealthybitesScreen: false });

        await buildNotificationSettings({ owner: user.id });

        response = await request(app)
          .post("/api/m2m/users/me/subscribe-wealthybites?didSubscribe=false")
          .send({})
          .set("external-user-id", user.id)
          .set("Accept", "application/json");
      });
      afterAll(_teardown);

      it("should return status 204 status", () => {
        expect(response.status).toEqual(204);
      });

      it("should update viewedReferralCodeScreen to true", async () => {
        const updatedUser = await User.findById(user._id);
        expect(updatedUser?.viewedWealthybitesScreen).toEqual(true);
      });

      it("should update the email notification settings of the user", async () => {
        const updatedNotificationSettings = await NotificationSettings.findOne({ owner: user._id });
        expect(updatedNotificationSettings.email.settings.get(EmailNotificationSettingEnum.WEALTHYBITES)).toEqual(
          false
        );
      });

      it("should emit event that user did not subscribe to wealthybites", () => {
        expect(eventEmitter.emit).toHaveBeenNthCalledWith(
          1,
          events.user.wealthybitesSubscription.eventId,
          expect.objectContaining({
            id: user.id
          }),
          { enabled: false }
        );
      });
    });
  });

  describe("POST /users/me/device-token", () => {
    let user: UserDocument;

    beforeAll(async () => {
      _startup();
      user = await buildUser();
    });
    afterAll(_teardown);

    it("should update the device token for a valid platform and token", async () => {
      const platform = "ios" as PlatformType;
      const deviceToken = "validDeviceToken";

      const response = await request(app)
        .post("/api/m2m/users/me/device-token")
        .send({ deviceToken })
        .set("external-user-id", user._id)
        .set("platform", "ios")
        .set("Accept", "application/json");

      expect(response.status).toEqual(204);

      const updatedUser = await User.findById(user._id);
      expect(updatedUser?.deviceTokens[platform]).toBe(deviceToken);
    });

    it("should return 500 if device token is missing", async () => {
      const response = await request(app)
        .post("/api/m2m/users/me/device-token")
        .set("external-user-id", user._id)
        .send({})
        .set("Accept", "application/json");

      expect(response.status).toEqual(500);
      expect(JSON.parse(response.text)).toMatchObject({
        error: {
          message: "Platform must be one of: android, ios"
        }
      });
    });

    it("should return 500 if platform is invalid", async () => {
      const deviceToken = "validDeviceToken";

      const response = await request(app)
        .post("/api/m2m/users/me/device-token")
        .set("external-user-id", user._id)
        .send({ deviceToken })
        .set("Accept", "application/json");

      expect(response.status).toEqual(500);
      expect(JSON.parse(response.text)).toMatchObject({
        error: {
          message: "Platform must be one of: android, ios"
        }
      });
    });
  });
});
