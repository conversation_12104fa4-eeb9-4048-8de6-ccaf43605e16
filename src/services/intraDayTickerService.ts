import Decimal from "decimal.js";
import { ClientSession } from "mongoose";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { DURATIONS_MAP, TenorEnum } from "../configs/durationConfig";
import eodService, { ExchangeRates } from "../external-services/eodService";
import { InvestmentProduct, InvestmentProductDocument } from "../models/InvestmentProduct";
import {
  IntraDayAssetTicker,
  IntraDayPortfolioTicker,
  IntraDayAssetTickerDocument,
  IntraDayPortfolioTickerDocument
} from "../models/IntraDayTicker";
import { PortfolioDocument, PortfolioPopulationFieldsEnum } from "../models/Portfolio";
import { UserDocument } from "../models/User";
import * as CacheUtil from "../utils/cacheUtil";
import CurrencyUtil from "../utils/currencyUtil";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";

export default class IntraDayTickerService {
  // ===============
  // PUBLIC METHODS
  // ===============

  public static async getMonthsTickersForPortfolio(
    portfolioId: string
  ): Promise<IntraDayPortfolioTickerDocument[]> {
    const { start } = DateUtil.getStartAndEndOfDay(
      DateUtil.getDateOfDaysAgo(new Date(Date.now()), DURATIONS_MAP[TenorEnum.ONE_MONTH])
    );

    return IntraDayPortfolioTicker.find({ portfolio: portfolioId, timestamp: { $gt: start } }).sort({
      timestamp: 1
    });
  }

  public static async getTodaysTickersForAsset(
    commonId: investmentUniverseConfig.AssetType
  ): Promise<IntraDayAssetTickerDocument[]> {
    const investmentProduct = await InvestmentProduct.findOne({ commonId });
    const { start } = DateUtil.getStartAndEndOfToday();

    return IntraDayAssetTicker.find({ investmentProduct, timestamp: { $gte: start } }).sort({ timestamp: 1 });
  }

  /**
   * @description Sets the daily portfolio ticker by creating one for the day if none exists or
   * by updating the existing one.
   *
   * @param portfolio
   * @param forcedNewValue
   * @param options
   */
  public static async setIntraDayPortfolioTicker(
    portfolio: PortfolioDocument,
    investmentProductsDict: Record<investmentUniverseConfig.AssetType, InvestmentProductDocument>,
    options?: {
      session: ClientSession;
    }
  ): Promise<void> {
    await DbUtil.populateIfNotAlreadyPopulated(portfolio, PortfolioPopulationFieldsEnum.OWNER);

    const latestFXRates = await CacheUtil.getCachedDataWithFallback<ExchangeRates>(
      "fxRates",
      async (): Promise<ExchangeRates> => eodService.getLatestFXRates()
    );

    const owner = portfolio.owner as UserDocument;
    // Portfolio value in user currency (settlement currency)
    const portfolioValue = new Decimal(
      portfolio.holdings
        .map(({ assetCommonId, quantity }) =>
          Decimal.mul(investmentProductsDict[assetCommonId].currentTicker.getPrice(owner.currency), quantity)
        )
        .reduce((sum, value) => sum.plus(value), new Decimal(0)) || new Decimal(0)
    )
      .toDecimalPlaces(2, Decimal.ROUND_DOWN)
      .toNumber();
    const portfolioPricePerCurrency = CurrencyUtil.mapFxDataToAmountPerCurrency(
      owner.currency ?? "GBP",
      portfolioValue,
      latestFXRates
    );

    await IntraDayPortfolioTicker.findOneAndUpdate(
      {
        portfolio: portfolio.id,
        timestamp: { $gt: DateUtil.getDateOfMinutesAgo(1) }
      },
      {
        currency: owner.currency,
        timestamp: new Date(),
        pricePerCurrency: portfolioPricePerCurrency,
        portfolio: portfolio.id
      },
      {
        upsert: true,
        session: options?.session
      }
    );
  }
}
