import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettings
} from "../../models/NotificationSettings";
import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import { buildNotificationSettings, buildUser } from "../../tests/utils/generateModels";
import NotificationSettingsService from "../notificationSettingsService";

describe("NotificationSettingsService", () => {
  beforeAll(async () => await connectDb("NotificationSettingsService"));
  afterAll(async () => {
    await clearDb();
    await closeDb();
  });

  describe("disableAllNotifications", () => {
    describe("when all notification settings are initially enabled", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        const appSettings = new Map(Object.values(AppNotificationSettingEnum).map((setting) => [setting, true]));
        const emailSettings = new Map(
          Object.values(EmailNotificationSettingEnum).map((setting) => [setting, true])
        );

        await buildNotificationSettings({
          owner: owner.id,
          app: {
            deviceNotificationsEnabled: false,
            settings: appSettings
          },
          email: {
            settings: emailSettings
          }
        });

        await NotificationSettingsService.disableAllNotifications(owner);
      });

      it("should disable all notifications", async () => {
        const notificationSettings = await NotificationSettings.findOne({ owner });

        notificationSettings!.app.settings.forEach((value) => expect(value).toBe(false));
        notificationSettings!.email.settings.forEach((value) => expect(value).toBe(false));
      });
    });
  });

  describe("hasAllowedEmailNotification", () => {
    describe("when email type is not one of those sent regardless of settings", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        const emailSettings = new Map(
          Object.values(EmailNotificationSettingEnum).map((setting) => [setting, false])
        );
        emailSettings.set(EmailNotificationSettingEnum.TRANSACTIONAL, true);

        await buildNotificationSettings({
          owner: owner.id,
          email: {
            settings: emailSettings
          }
        });
      });

      it("should allow email notification for enabled setting", async () => {
        const result = await NotificationSettingsService.hasAllowedEmailNotification(owner, "automatedRebalance");

        expect(result).toBe(true);
      });

      it("should not allow email notification for disabled setting", async () => {
        await NotificationSettings.findOneAndUpdate(
          { owner },
          { email: { settings: { email_transactional: false } } }
        );
        const result = await NotificationSettingsService.hasAllowedEmailNotification(owner, "automatedRebalance");

        expect(result).toBe(false);
      });
    });

    describe("when email type is one of those sent regardless of setting", () => {
      let owner: UserDocument;

      beforeAll(async () => {
        owner = await buildUser();

        const emailSettings = new Map(
          Object.values(EmailNotificationSettingEnum).map((setting) => [setting, false])
        );

        await buildNotificationSettings({
          owner: owner.id,
          email: {
            settings: emailSettings
          }
        });
      });

      it("should allow email notification for disabled setting", async () => {
        const result = await NotificationSettingsService.hasAllowedEmailNotification(owner, "deletionSuccess");

        expect(result).toBe(true);
      });
    });
  });
});
