import { UserDocument } from "../../models/User";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildHoldingDTO,
  buildPortfolio,
  buildRiskAssessment,
  buildUser
} from "../../tests/utils/generateModels";

import { entitiesConfig } from "@wealthyhood/shared-configs";
import { AmlScreeningResultEnum } from "../../configs/riskAssessmentConfig";
import events from "../../event-handlers/events";
import { CurrencyEnum } from "../../external-services/wealthkernelService";
import eventEmitter from "../../loaders/eventEmitter";
import { RiskAssessment, RiskAssessmentDocument, RiskScoreClassificationEnum } from "../../models/RiskAssessment";
import RiskAssessmentService from "../riskAssessmentService";

const originalDateNow = Date.now;

describe("RiskAssessmentService", () => {
  beforeAll(async () => await connectDb("RiskAssessmentService"));
  afterAll(async () => await closeDb());

  describe("createRiskAssessment", () => {
    describe("when a user is not verified with no previous risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "pending",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
      });

      it("should not create a risk assessment for the user and throw an error", async () => {
        await expect(async () => await RiskAssessmentService.createRiskAssessment(user)).rejects.toThrow(
          new Error("To calculate risk assessment user must be verified")
        );
        const riskAssessments = await RiskAssessment.find();

        expect(riskAssessments).toHaveLength(0);
      });
    });

    describe("when user has a bank account with a Greek IBAN", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GR",
          nationalities: ["GR"],
          employmentInfo: {
            incomeRangeId: "2",
            annualIncome: {
              amount: 10000,
              currency: CurrencyEnum.EUR
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });

        await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([buildHoldingDTO(true, "equities_apple", 1, { price: 1000 })])
        });

        await buildBankAccount({
          owner: user.id,
          iban: "********************************"
        });

        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should score the bank account as zero-risk since it belongs to a European country", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(1);
        expect(riskAssessments[0].sourcesOfFunds).toEqual(
          expect.objectContaining({ value: ["LinkedEUBankAccount"], score: 0 })
        );
      });
    });

    describe("when user is verified and has no previous risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(1);
      });

      it("should emit an event", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        const riskAssessment = riskAssessments[0];

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: riskAssessment.nationality,
            sourcesOfFunds: riskAssessment.sourcesOfFunds,
            sourcesOfWealth: riskAssessment.sourcesOfWealth,
            employmentStatus: riskAssessment.employmentStatus,
            volumeOfTransactions: riskAssessment.volumeOfTransactions,
            amlScreening: riskAssessment.amlScreening,
            totalScore: riskAssessment.totalScore,
            classification: "Low risk"
          })
        );
      });
    });

    describe("when user is verified and has previous risk assessment", () => {
      let user: UserDocument;
      const RISK_ASSESSMENT = new Date("2024-01-01T10:49:00Z");
      const DATE_NOW = new Date("2024-03-03T10:49:00Z");

      beforeAll(async () => {
        await clearDb();
        jest.clearAllMocks();
        Date.now = jest.fn(() => DATE_NOW.valueOf());

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });
        await buildRiskAssessment({
          createdAt: RISK_ASSESSMENT,
          owner: user.id,
          volumeOfTransactions: {
            value: 7000,
            score: 5
          }
        });
        // user has made an asset transaction after the latest valuation
        await buildAssetTransaction({
          owner: user.id,
          status: "Settled",
          settledAt: new Date(),
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 400000,
          consideration: {
            amount: 400000,
            currency: "GBP"
          }
        });
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a new risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id }).sort({ createdAt: -1 }); // Sorting in descending order by createdAt

        expect(riskAssessments).toHaveLength(2);
      });

      it("should use the previous risk assessment to calculate the new one", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id }).sort({ createdAt: -1 }); // Sorting in descending order by createdAt

        expect(riskAssessments[0]).toEqual(
          expect.objectContaining({
            nationality: { value: "GB", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["Salary"], score: 0 },
            employmentStatus: { value: "FullTime", score: 0 },
            volumeOfTransactions: { value: 11000, score: 8 }, // 7000 from previous + 4000 from the new one
            amlScreening: { value: "NoHit", score: 0 },
            totalScore: 8
          })
        );
      });
    });

    describe("when user is verified and has mock employment info", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date("2020-01-01"),
          nationalities: ["GB"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "1",
            annualIncome: {
              amount: 1,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "Student",
            sourcesOfWealth: ["SaleOfInvestments"]
          }
        });

        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment with a risk score of 0 for score relevant to employment data for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessments).toHaveLength(1);

        expect(JSON.parse(JSON.stringify(riskAssessments[0]))).toEqual(
          expect.objectContaining({
            nationality: { value: "GB", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["SaleOfInvestments"], score: 0 },
            employmentStatus: { value: "Student", score: 0 },
            totalScore: 0
          })
        );
      });
    });

    describe("when user is High risk and the last risk assessment is Low Risk", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["GB"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        const previousRiskAssessment = await buildRiskAssessment({
          createdAt: new Date(),
          owner: user.id,
          volumeOfTransactions: {
            value: 7000,
            score: 5
          }
        });
        expect(previousRiskAssessment.classification).toEqual(RiskScoreClassificationEnum.LowRisk);
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(2);
      });

      it("should emit risk assessment created event", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id }).sort({ createdAt: -1 });
        const riskAssessment = riskAssessments[0];

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: riskAssessment.nationality,
            sourcesOfFunds: riskAssessment.sourcesOfFunds,
            sourcesOfWealth: riskAssessment.sourcesOfWealth,
            employmentStatus: riskAssessment.employmentStatus,
            volumeOfTransactions: riskAssessment.volumeOfTransactions,
            amlScreening: riskAssessment.amlScreening,
            totalScore: riskAssessment.totalScore,
            classification: "High risk"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is High risk and the last risk assessment is Prohibited", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["AL"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        const previousRiskAssessment = await buildRiskAssessment({
          createdAt: new Date(),
          owner: user.id,
          amlScreening: {
            value: AmlScreeningResultEnum.SanctionsListHit,
            score: 100
          },
          totalScore: 105
        });
        expect(previousRiskAssessment.classification).toEqual(RiskScoreClassificationEnum.Prohibited);
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(2);
      });

      it("should emit risk assessment created event", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id }).sort({ createdAt: -1 });
        const riskAssessment = riskAssessments[0];

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: riskAssessment.nationality,
            sourcesOfFunds: riskAssessment.sourcesOfFunds,
            sourcesOfWealth: riskAssessment.sourcesOfWealth,
            employmentStatus: riskAssessment.employmentStatus,
            volumeOfTransactions: riskAssessment.volumeOfTransactions,
            amlScreening: riskAssessment.amlScreening,
            totalScore: riskAssessment.totalScore,
            classification: "High risk"
          })
        );
      });

      it("should not emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).not.toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is High risk and has no previous risk assessment", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["AL"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(1);
      });

      it("should emit risk assessment created event", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        const riskAssessment = riskAssessments[0];

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: riskAssessment.nationality,
            sourcesOfFunds: riskAssessment.sourcesOfFunds,
            sourcesOfWealth: riskAssessment.sourcesOfWealth,
            employmentStatus: riskAssessment.employmentStatus,
            volumeOfTransactions: riskAssessment.volumeOfTransactions,
            amlScreening: riskAssessment.amlScreening,
            totalScore: riskAssessment.totalScore,
            classification: "High risk"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is Prohibited risk and has no previous risk assessment", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["BY"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          amlScreening: AmlScreeningResultEnum.SanctionsListHit,
          employmentInfo: {
            incomeRangeId: "10",
            annualIncome: {
              amount: 500000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(1);
      });

      it("should emit risk assessment created event", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        const riskAssessment = riskAssessments[0];

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: riskAssessment.nationality,
            sourcesOfFunds: riskAssessment.sourcesOfFunds,
            sourcesOfWealth: riskAssessment.sourcesOfWealth,
            employmentStatus: riskAssessment.employmentStatus,
            volumeOfTransactions: riskAssessment.volumeOfTransactions,
            amlScreening: riskAssessment.amlScreening,
            totalScore: riskAssessment.totalScore,
            classification: "Prohibited"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is flagged as a PEP (GB) and in the previous assessment he was not", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["GB"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          amlScreening: AmlScreeningResultEnum.MaterialAdverseMediaPEP,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        const previousRiskAssessment = await buildRiskAssessment({
          createdAt: new Date(),
          owner: user.id,
          volumeOfTransactions: {
            value: 7000,
            score: 5
          }
        });
        expect(previousRiskAssessment.classification).toEqual(RiskScoreClassificationEnum.LowRisk);
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(2);
      });

      it("should emit risk assessment created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: { value: "GB", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["Gift"], score: 10 },
            employmentStatus: { value: "NotWorkingDueToIllnessOrDisability", score: 10 },
            volumeOfTransactions: { value: 7000, score: 5 },
            amlScreening: { value: "MaterialAdverseMediaPEP", score: 10 },
            totalScore: 35,
            classification: "High risk"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is flagged as a PEP (NON-GB) and in the previous assessment he was not", () => {
      let user: UserDocument;
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          nationalities: ["AU"],
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          amlScreening: AmlScreeningResultEnum.MaterialAdverseMediaPEP,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "NotWorkingDueToIllnessOrDisability",
            sourcesOfWealth: ["Gift"]
          }
        });
        await buildAssetTransaction({
          owner: user.id,
          settledAt: new Date(),
          status: "Settled",
          portfolioTransactionCategory: "buy",
          consideration: {
            amount: 200000,
            currency: "GBP"
          },
          originalInvestmentAmount: 200000
        });
        const previousRiskAssessment = await buildRiskAssessment({
          createdAt: new Date(),
          owner: user.id,
          volumeOfTransactions: {
            value: 7000,
            score: 5
          }
        });
        expect(previousRiskAssessment.classification).toEqual(RiskScoreClassificationEnum.LowRisk);
        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a risk assessment for the user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments).toHaveLength(2);
      });

      it("should emit risk assessment created event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.riskAssessmentCreated.eventId,
          expect.objectContaining({ email: user.email }),
          expect.objectContaining({
            nationality: { value: "AU", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["Gift"], score: 10 },
            employmentStatus: { value: "NotWorkingDueToIllnessOrDisability", score: 10 },
            volumeOfTransactions: { value: 7000, score: 5 },
            amlScreening: { value: "MaterialAdverseMediaPEP", score: 15 },
            totalScore: 40,
            classification: "High risk"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is in EU entity, is low risk, has <10k account value and has no previous risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GB",
          nationalities: ["AL"], // high risk country
          employmentInfo: {
            incomeRangeId: "2",
            annualIncome: {
              amount: 10000,
              currency: CurrencyEnum.EUR
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });

        await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([buildHoldingDTO(true, "equities_apple", 1, { price: 1000 })])
        });

        // User has an EU bank account
        await buildBankAccount({
          owner: user.id,
          iban: "********************************"
        });

        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a high risk assessment for the EU user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessments).toHaveLength(1);

        expect(riskAssessments[0].toObject()).toEqual(
          expect.objectContaining({
            dueDiligenceClassification: "Simplified"
          })
        );
      });
    });

    describe("when user is in EU entity, is high risk, has <10k account value and has no previous risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GB",
          nationalities: ["AL"], // high risk country
          employmentInfo: {
            incomeRangeId: "2",
            annualIncome: {
              amount: 10000,
              currency: CurrencyEnum.EUR
            },
            employmentStatus: "Unemployed",
            sourcesOfWealth: ["Gift"]
          }
        });

        await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([buildHoldingDTO(true, "equities_apple", 1, { price: 1000 })])
        });

        // User has a a non-EU bank account
        await buildBankAccount({
          owner: user.id,
          iban: "********************************"
        });

        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a high risk assessment for the EU user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessments).toHaveLength(1);

        expect(riskAssessments[0].toObject()).toEqual(
          expect.objectContaining({
            totalScore: 16,
            nationality: expect.objectContaining({
              score: 0, // For EU users, nationality score is based on residency country, not nationality
              value: "GB"
            }),
            employmentStatus: expect.objectContaining({
              score: 5,
              value: "Unemployed"
            }),
            sourcesOfWealth: expect.objectContaining({
              score: 5,
              value: ["Gift"]
            }),
            sourcesOfFunds: expect.objectContaining({
              score: 6,
              value: ["LinkedNonEUBankAccount"]
            }),
            dueDiligenceClassification: "Standard"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });

    describe("when user is in EU entity, is high risk, has more than 75k account value and has no previous risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE,
          residencyCountry: "GB",
          nationalities: ["AL"], // high risk country
          employmentInfo: {
            incomeRangeId: "2",
            annualIncome: {
              amount: 10000,
              currency: CurrencyEnum.EUR
            },
            employmentStatus: "Unemployed",
            sourcesOfWealth: ["Gift"]
          }
        });

        await buildPortfolio({
          owner: user.id,
          holdings: await Promise.all([buildHoldingDTO(true, "equities_apple", 200, { price: 1000 })]) // 200k value
        });

        // User has a a non-EU bank account
        await buildBankAccount({
          owner: user.id,
          iban: "********************************"
        });

        await RiskAssessmentService.createRiskAssessment(user);
      });

      it("should create a high risk assessment for the EU user", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessments).toHaveLength(1);

        expect(riskAssessments[0].toObject()).toEqual(
          expect.objectContaining({
            totalScore: 16,
            nationality: expect.objectContaining({
              score: 0, // For EU users, nationality score is based on residency country, not nationality
              value: "GB"
            }),
            employmentStatus: expect.objectContaining({
              score: 5,
              value: "Unemployed"
            }),
            sourcesOfWealth: expect.objectContaining({
              score: 5,
              value: ["Gift"]
            }),
            sourcesOfFunds: expect.objectContaining({
              score: 6,
              value: ["LinkedNonEUBankAccount"]
            }),
            dueDiligenceClassification: "Enhanced"
          })
        );
      });

      it("should emit high risk assessment detected event", async () => {
        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.user.highRiskAssessmentDetected.eventId,
          expect.objectContaining({ email: user.email })
        );
      });
    });
  });

  describe("createRiskAssessments", () => {
    describe("when there is a user that is not verified", () => {
      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();

        await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "pending",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        await RiskAssessmentService.createRiskAssessments();
      });

      it("should not create a risk assessment", async () => {
        const riskAssessment = await RiskAssessment.find();
        expect(riskAssessment.length).toBe(0);
      });
    });

    describe("when a user is verified but has deleted his account", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          email: "<EMAIL>",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });

        await RiskAssessmentService.createRiskAssessments();
      });

      it("should not create a risk assessment", async () => {
        const riskAssessment = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessment.length).toBe(0);
      });
    });

    describe("when there is a user that has risk assessment for today", () => {
      let user: UserDocument;
      const DATE_NOW = new Date("2024-03-03T10:49:00Z");

      beforeAll(async () => {
        await clearDb();
        jest.clearAllMocks();
        Date.now = jest.fn(() => DATE_NOW.valueOf());

        user = await buildUser({
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        await buildRiskAssessment({
          owner: user.id,
          createdAt: new Date(Date.now())
        });
        await RiskAssessmentService.createRiskAssessments();
      });

      it("should not create a risk assessment", async () => {
        const riskAssessment = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessment.length).toBe(1);
      });
    });

    describe("when there is an eligible user for risk assessment", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });
        await RiskAssessmentService.createRiskAssessments();
      });

      it("should create a risk assessment", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });

        expect(riskAssessments.length).toBe(1);
        expect(riskAssessments[0]).toEqual(
          expect.objectContaining({
            nationality: { value: "GB", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["Salary"], score: 0 },
            employmentStatus: { value: "FullTime", score: 0 },
            volumeOfTransactions: { value: 0, score: 0 },
            amlScreening: { value: "NoHit", score: 0 },
            totalScore: 0
          })
        );
      });
    });

    describe("when there is an eligible user for risk assessment and it runs twice", () => {
      let user: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();
        Date.now = jest.fn(originalDateNow);

        user = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "FullTime",
            sourcesOfWealth: ["Salary"]
          }
        });
        await RiskAssessmentService.createRiskAssessments();
        await RiskAssessmentService.createRiskAssessments();
      });

      it("should create only one risk assessment", async () => {
        const riskAssessments = await RiskAssessment.find({ owner: user.id });
        expect(riskAssessments.length).toBe(1);
        expect(riskAssessments[0]).toEqual(
          expect.objectContaining({
            nationality: { value: "GB", score: 0 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["Salary"], score: 0 },
            employmentStatus: { value: "FullTime", score: 0 },
            volumeOfTransactions: { value: 0, score: 0 },
            amlScreening: { value: "NoHit", score: 0 },
            totalScore: 0
          })
        );
      });
    });

    describe("when there are multiple eligible users", () => {
      let user1: UserDocument;
      let user2: UserDocument;

      beforeAll(async () => {
        await clearDb();
        jest.resetAllMocks();

        user1 = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          nationalities: ["MT"], // risk score => 11
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            employmentStatus: "PartTime", // risk score => 3
            sourcesOfWealth: ["SaleOfProperty"] // risk score => 4
          },
          amlScreening: AmlScreeningResultEnum.NoHit
        });
        user2 = await buildUser({
          submittedRequiredInfoAt: new Date(),
          kycStatus: "passed",
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
          nationalities: ["HU"], // risk score => 6
          employmentInfo: {
            incomeRangeId: "3",
            annualIncome: {
              amount: 25000,
              currency: CurrencyEnum.GBP
            },
            sourcesOfWealth: ["PersonalSavings", "BusinessOwnership"], // risk score => 3 & 2 => 3
            employmentStatus: "Student" // risk score => 5
          },
          amlScreening: AmlScreeningResultEnum.ImmaterialAdverseMedia // risk score =>5
        });
        await buildAssetTransaction({
          owner: user2.id,
          status: "Settled",
          settledAt: new Date(),
          portfolioTransactionCategory: "buy",
          originalInvestmentAmount: 400000,
          consideration: {
            amount: 400000,
            currency: "GBP"
          }
        }); // risk score => 2
        await buildUser({
          kycStatus: "passed",
          submittedRequiredInfoAt: new Date(),
          companyEntity: entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        });
        await RiskAssessmentService.createRiskAssessments();
      });

      it("should create two risk assessments", async () => {
        const riskAssessmentsUser1: RiskAssessmentDocument[] = await RiskAssessment.find({ owner: user1.id });
        const riskAssessmentsUser2: RiskAssessmentDocument[] = await RiskAssessment.find({ owner: user2.id });

        const riskAssessmentUser1 = riskAssessmentsUser1[0];
        const riskAssessmentUser2 = riskAssessmentsUser2[0];

        expect(riskAssessmentUser1).toEqual(
          expect.objectContaining({
            nationality: { value: "MT", score: 11 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["SaleOfProperty"], score: 4 },
            employmentStatus: { value: "PartTime", score: 3 },
            volumeOfTransactions: { value: 0, score: 0 },
            amlScreening: { value: "NoHit", score: 0 },
            totalScore: 18
          })
        );

        expect(riskAssessmentUser2).toEqual(
          expect.objectContaining({
            nationality: { value: "HU", score: 6 },
            sourcesOfFunds: { value: ["LinkedUKBankAccount"], score: 0 },
            sourcesOfWealth: { value: ["PersonalSavings", "BusinessOwnership"], score: 3 },
            employmentStatus: { value: "Student", score: 5 },
            volumeOfTransactions: { value: 4000, score: 2 },
            amlScreening: { value: "ImmaterialAdverseMedia", score: 5 },
            totalScore: 21
          })
        );
      });
    });
  });
});
