import { faker } from "@faker-js/faker";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";
import { clearDb, closeDb, connectDb } from "../../../tests/utils/db";
import {
  buildAssetTransaction,
  buildChargeTransaction,
  buildDividendTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildOrder,
  buildPortfolio,
  buildSubscription,
  buildUser,
  buildReward,
  buildDailyPortfolioTicker
} from "../../../tests/utils/generateModels";
import { TenorEnum } from "../../../configs/durationConfig";
import PortfolioService from "../../portfolioService";
import { PortfolioDocument, PortfolioModeEnum } from "../../../models/Portfolio";
import { DividendTransactionDocument } from "../../../models/Transaction";
import { RewardDocument } from "../../../models/Reward";
import { UserDocument } from "../../../models/User";
import DateUtil from "../../../utils/dateUtil";
import { ProviderEnum } from "../../../configs/providersConfig";

describe("PortfolioService.calculateUpByValues", () => {
  beforeAll(async () => {
    await connectDb("calculateUpByValues");
  });
  afterAll(async () => {
    await closeDb();
  });
  afterEach(async () => await clearDb());

  describe("when requested with an ALL_TIME tenor", () => {
    describe("when user has a portfolio buy transaction", () => {
      const PORTFOLIO_VALUE = 110;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        const assetTransaction = await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: SETTLED_DATE
        });
        const order = await buildOrder({
          status: "Matched",
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE,
          transaction: assetTransaction.id,
          side: "Buy",
          consideration: {
            originalAmount: BUY_AMOUNT * 100,
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          updatedAt: SETTLED_DATE
        });
        assetTransaction.orders = [order];
        await assetTransaction.save();
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 110 - 100
      });
    });

    describe("when user has an asset buy transaction", () => {
      const PORTFOLIO_VALUE = 110;
      const BUY_AMOUNT = 100;

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };

      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });

        // Create an asset buy transaction for £100
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" },
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: BUY_AMOUNT * 100,
              amountSubmitted: BUY_AMOUNT * 100,
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            quantity: 1
          })
        ];

        await assetBuyTransaction.save();
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 110 - 100
      });
    });

    describe("when user has an old portfolio update transaction with multiple orders", () => {
      const PORTFOLIO_VALUE = 210;
      const BUY_AMOUNT = 100;

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });

        const portfolioUpdateTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" },
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
        portfolioUpdateTransaction.orders = [
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: portfolioUpdateTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: BUY_AMOUNT * 100,
              amountSubmitted: BUY_AMOUNT * 100,
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            quantity: 1
          }),
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: portfolioUpdateTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
            consideration: {
              originalAmount: BUY_AMOUNT * 100,
              amountSubmitted: BUY_AMOUNT * 100,
              amount: BUY_AMOUNT * 100,
              currency: "GBP"
            },
            quantity: 1
          })
        ];

        await portfolioUpdateTransaction.save();
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 110 - 100
      });
    });

    describe("when user has multiple asset transactions", () => {
      const PORTFOLIO_VALUE = 110;
      const PORTFOLIO_BUY_AMOUNT = 100;
      const ASSET_BUY_AMOUNT = 100;
      const SELL_AMOUNT = 100;

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });

        // Create a portfolio buy transaction for £100
        await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: PORTFOLIO_BUY_AMOUNT * 100
          },
          originalInvestmentAmount: PORTFOLIO_BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: new Date("2022-07-17T11:00:00Z")
        });

        // Create an asset buy transaction for £100
        const assetBuyTransaction = await buildAssetTransaction({
          owner: user.id,
          portfolio: portfolio.id,
          status: "Settled",
          portfolioTransactionCategory: "update",
          consideration: { currency: "GBP" },
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
        assetBuyTransaction.orders = [
          await buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: { id: faker.string.uuid(), status: "Matched", submittedAt: new Date() }
            },
            side: "Buy",
            transaction: assetBuyTransaction.id,
            isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              originalAmount: ASSET_BUY_AMOUNT * 100,
              amountSubmitted: ASSET_BUY_AMOUNT * 100,
              amount: ASSET_BUY_AMOUNT * 100,
              currency: "GBP"
            },
            quantity: 1
          })
        ];

        await assetBuyTransaction.save();

        // Create a portfolio sell transaction for £100
        await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: SELL_AMOUNT * 100
          },
          owner: user.id,
          portfolioTransactionCategory: "sell",
          status: "Settled",
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 110 + 100 - 200 (portfolio value + total sells - total buys)
      });
    });

    describe("when user has been charged from holdings", () => {
      const PORTFOLIO_VALUE = 109;
      const CHARGE_VALUE = 1;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly"
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        const assetTransaction = await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: SETTLED_DATE
        });
        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        const chargeTransaction = await buildChargeTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            holdingsAmount: CHARGE_VALUE * 100,
            amount: CHARGE_VALUE * 100
          },
          owner: user.id,
          subscription: subscription.id,
          chargeMethod: "holdings",
          chargeType: "subscription",
          status: "Settled",
          settledAt: SETTLED_DATE
        });
        await buildOrder({
          status: "Matched",
          side: "Sell",
          transaction: chargeTransaction.id,
          consideration: {
            amount: CHARGE_VALUE * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        await chargeTransaction.populate("orders");
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 109 + 1 - 100
      });
    });

    describe("when user been charged from holdings but charge order has not been submitted to WK yet", () => {
      const PORTFOLIO_VALUE = 109;
      const CHARGE_VALUE = 1;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        const subscription = await buildSubscription({
          owner: user.id,
          price: "free_monthly"
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        const assetTransaction = await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        const chargeTransaction = await buildChargeTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            holdingsAmount: CHARGE_VALUE * 100,
            amount: CHARGE_VALUE * 100
          },
          owner: user.id,
          subscription: subscription.id,
          chargeMethod: "holdings",
          chargeType: "subscription",
          status: "Pending",
          settledAt: new Date("2022-07-17T11:00:00Z")
        });
        await buildOrder({
          status: "Pending",
          side: "Sell",
          transaction: chargeTransaction.id,
          consideration: {
            amount: CHARGE_VALUE * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Pending",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        await chargeTransaction.populate("orders");
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 9 }); // 109 - 100
      });
    });

    describe("when user has been given a reward", () => {
      const PORTFOLIO_VALUE = 110;
      const REWARD_VALUE = 10;
      const BUY_AMOUNT = 90;
      const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        const assetTransaction = await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: SETTLED_DATE
        });
        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        const reward = await buildReward({
          targetUser: user.id,
          referral: user.id,
          consideration: {
            currency: "GBP",
            amount: REWARD_VALUE * 100,
            orderAmount: REWARD_VALUE * 100,
            bonusAmount: REWARD_VALUE * 100
          },
          asset: "equities_uk",
          quantity: 1,
          status: "Settled",
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Matched"
              }
            }
          },
          updatedAt: SETTLED_DATE
        });
        transactions.rewards = [reward];
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 10 }); // 100 + 10 - 100
      });
    });

    describe("when user has received a dividend", () => {
      const PORTFOLIO_VALUE = 110;
      const DIVIDEND_VALUE = 1;
      const BUY_AMOUNT = 100;
      const SETTLED_DATE = new Date("2022-07-17T11:00:00Z");

      const transactions: {
        dividendTransactions: DividendTransactionDocument[];
        rewards: RewardDocument[];
      } = {
        dividendTransactions: [],
        rewards: []
      };
      let user: UserDocument;
      let portfolio: PortfolioDocument;

      beforeEach(async () => {
        user = await buildUser();
        portfolio = await buildPortfolio({
          owner: user.id,
          mode: PortfolioModeEnum.REAL,
          cash: {
            GBP: { available: 0, reserved: 0, settled: 0 }
          },
          holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
        });
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE }
        });
        const assetTransaction = await buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: SETTLED_DATE
        });
        const assetBuyOrder = await buildOrder({
          status: "Matched",
          side: "Buy",
          transaction: assetTransaction.id,
          consideration: {
            amount: BUY_AMOUNT * 100,
            currency: "GBP"
          },
          providers: {
            wealthkernel: {
              id: faker.string.uuid(),
              status: "Matched",
              submittedAt: SETTLED_DATE
            }
          },
          filledAt: SETTLED_DATE
        });
        assetTransaction.orders = [assetBuyOrder];
        await assetTransaction.save();

        const dividendTransaction = await buildDividendTransaction({
          portfolio: portfolio.id,
          consideration: {
            amount: DIVIDEND_VALUE * 100,
            currency: "GBP"
          },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } },
          createdAt: SETTLED_DATE
        });
        transactions.dividendTransactions = [dividendTransaction];
      });

      it("should return an up-by number value for all time", async () => {
        const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ALL_TIME);
        expect(upBy).toEqual({ max: 11 }); // 110 - 100 + 1
      });
    });
  });

  describe("when requested with a ONE_WEEK tenor and user has a portfolio buy transaction exactly 7 days ago", () => {
    const PORTFOLIO_VALUE = 110;
    const BUY_AMOUNT = 100;
    const SETTLED_DATE = new Date("2024-01-01T23:00:00Z");

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = "2024-01-08";
      Date.now = jest.fn(() => +new Date(TODAY));

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        },
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
      });

      const [assetTransaction] = await Promise.all([
        buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: SETTLED_DATE
        }),
        buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE },
          timestamp: SETTLED_DATE
        }),
        buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 0 },
          timestamp: DateUtil.getStartOfDay(SETTLED_DATE)
        })
      ]);

      const assetBuyOrder = await buildOrder({
        status: "Matched",
        side: "Buy",
        transaction: assetTransaction.id,
        consideration: {
          amount: BUY_AMOUNT * 100,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: SETTLED_DATE
          }
        },
        filledAt: SETTLED_DATE
      });
      assetTransaction.orders = [assetBuyOrder];
      await assetTransaction.save();
    });

    it("should return an up-by number value for one week", async () => {
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ONE_WEEK);
      expect(upBy).toEqual({ "1w": 10 }); // 110 - 100
    });
  });

  describe("when requested with a ONE_WEEK tenor and asset transaction created outside tenor but order matched within tenor", () => {
    const PORTFOLIO_VALUE = 110;
    const BUY_AMOUNT = 100;
    const TRANSACTION_CREATED_DATE = new Date("2023-12-25T11:00:00Z"); // 10 days before tenor start
    const ORDER_FILLED_DATE = new Date("2024-01-03T11:00:00Z"); // Within the one week tenor

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = "2024-01-08";
      Date.now = jest.fn(() => +new Date(TODAY));

      user = await buildUser();
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        },
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: PORTFOLIO_VALUE })]
      });

      const [assetTransaction] = await Promise.all([
        buildAssetTransaction({
          portfolio: portfolio.id,
          consideration: {
            currency: "GBP",
            amount: BUY_AMOUNT * 100
          },
          originalInvestmentAmount: BUY_AMOUNT * 100,
          owner: user.id,
          portfolioTransactionCategory: "buy",
          status: "Settled",
          settledAt: TRANSACTION_CREATED_DATE,
          createdAt: TRANSACTION_CREATED_DATE
        }),
        buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: PORTFOLIO_VALUE },
          timestamp: new Date(TODAY)
        }),
        buildIntraDayPortfolioTicker({
          portfolio: portfolio.id,
          pricePerCurrency: { GBP: 0 },
          timestamp: DateUtil.getStartOfDay(DateUtil.getDateOfDaysAgo(new Date(TODAY), 7))
        })
      ]);

      const assetBuyOrder = await buildOrder({
        status: "Matched",
        side: "Buy",
        transaction: assetTransaction.id,
        consideration: {
          amount: BUY_AMOUNT * 100,
          currency: "GBP"
        },
        providers: {
          wealthkernel: {
            id: faker.string.uuid(),
            status: "Matched",
            submittedAt: ORDER_FILLED_DATE
          }
        },
        filledAt: ORDER_FILLED_DATE
      });
      assetTransaction.orders = [assetBuyOrder];
      await assetTransaction.save();
    });

    it("should include the order in up-by calculation when order is filled within tenor", async () => {
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.ONE_WEEK);
      expect(upBy).toEqual({ "1w": 10 }); // 110 - 100, order should be included because filledAt is within tenor
    });
  });

  describe("when requested with a TODAY tenor but the portfolio has no ticker for the day", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 100;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = "2024-03-31";
      Date.now = jest.fn(() => +new Date(TODAY));

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        }
      });
      await Promise.all([
        buildIntraDayPortfolioTicker({
          timestamp: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        })
      ]);
    });

    it("should return 0 up-by for today", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
      expect(upBy).toEqual({ today: 0 });
    });
  });

  describe("when requested with a TODAY tenor and the portfolio has multiple tickers for the day", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 100;
    const END_INTRADAY_PORTFOLIO_VALUE = 150;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = new Date("2024-03-31T04:20:00Z");
      const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
      Date.now = jest.fn(() => +TODAY);

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        },
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })]
      });

      await Promise.all([
        buildDailyPortfolioTicker({
          date: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildDailyPortfolioTicker({
          date: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        })
      ]);

      const ordersConfig = [
        {
          side: "Buy",
          amount: 400,
          updatedAt: DateUtil.getDateOfDaysAgo(TODAY, 1)
        }
      ];
      const transactionSettledYesterday = await buildAssetTransaction({
        owner: user.id,
        status: "Settled"
      });
      const orderFilledYesterday = await Promise.all(
        ordersConfig.map(({ side, amount, updatedAt }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                status: "Matched",
                id: faker.string.uuid(),
                submittedAt: updatedAt
              }
            },
            filledAt: updatedAt,
            transaction: transactionSettledYesterday.id,
            side: side as "Buy" | "Sell",
            consideration: {
              amount: amount - 1,
              originalAmount: amount,
              currency: "GBP"
            },
            fees: {
              executionSpread: {
                amount: 1,
                currency: "GBP"
              },
              fx: {
                amount: 0,
                currency: "GBP"
              }
            },
            updatedAt
          })
        )
      );
      transactionSettledYesterday.orders = orderFilledYesterday.map((order) => order.id);
      await transactionSettledYesterday.save();
      await transactionSettledYesterday.populate("orders");
    });

    it("should return the correct up-by for today", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
      expect(upBy).toEqual({ today: 50 });
    });
  });

  describe("when requested with a TODAY tenor and the portfolio has multiple tickers for the day and it is the first day the user is invested", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 100;
    const END_INTRADAY_PORTFOLIO_VALUE = 150;

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = new Date("2024-03-31T04:20:00Z");
      const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
      Date.now = jest.fn(() => +TODAY);

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        },
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })]
      });

      await Promise.all([
        buildIntraDayPortfolioTicker({
          timestamp: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        })
      ]);

      const ordersConfig = [
        {
          side: "Buy",
          amount: START_INTRADAY_PORTFOLIO_VALUE * 100,
          updatedAt: TODAY
        }
      ];
      const transactionSettledToday = await buildAssetTransaction({
        owner: user.id,
        status: "Settled"
      });
      const orderFilledToday = await Promise.all(
        ordersConfig.map(({ side, amount, updatedAt }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                status: "Matched",
                id: faker.string.uuid(),
                submittedAt: updatedAt
              }
            },
            filledAt: updatedAt,
            transaction: transactionSettledToday.id,
            side: side as "Buy" | "Sell",
            consideration: {
              amount: amount - 1,
              originalAmount: amount,
              currency: "GBP"
            },
            fees: {
              executionSpread: {
                amount: 0,
                currency: "GBP"
              },
              fx: {
                amount: 0,
                currency: "GBP"
              }
            },
            updatedAt
          })
        )
      );
      transactionSettledToday.orders = orderFilledToday.map((order) => order.id);
      await transactionSettledToday.save();
      await transactionSettledToday.populate("orders");
    });

    it("should return the correct up-by for today", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
      expect(upBy).toEqual({ today: 50 });
    });
  });

  describe("when requested with a TODAY tenor and there is a buy order with real time execution fees", () => {
    const START_INTRADAY_PORTFOLIO_VALUE = 10; // Whole
    const ORDER_BUY_AMOUNT_PRE_REAL_TIME_FEE = 400; // Cents
    const REAL_TIME_EXECUTION_FEE = 100; // Cents
    const END_INTRADAY_PORTFOLIO_VALUE = 15; // Whole

    const transactions: {
      dividendTransactions: DividendTransactionDocument[];
      rewards: RewardDocument[];
    } = {
      dividendTransactions: [],
      rewards: []
    };
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      const TODAY = new Date("2024-03-31T04:20:00Z");
      const LATER_TODAY = new Date("2024-03-31T12:20:00Z");
      Date.now = jest.fn(() => +TODAY);

      user = await buildUser({ currency: "GBP", portfolioConversionStatus: "completed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        cash: {
          GBP: { available: 0, reserved: 0, settled: 0 }
        },
        holdings: [await buildHoldingDTO(true, "equities_eu", 1, { price: END_INTRADAY_PORTFOLIO_VALUE })]
      });

      await Promise.all([
        buildDailyPortfolioTicker({
          date: DateUtil.getDateOfDaysAgo(new Date(TODAY), 1),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildDailyPortfolioTicker({
          date: new Date(TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: START_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: START_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: START_INTRADAY_PORTFOLIO_VALUE
          }
        }),
        buildIntraDayPortfolioTicker({
          timestamp: new Date(LATER_TODAY),
          portfolio: portfolio.id,
          pricePerCurrency: {
            USD: END_INTRADAY_PORTFOLIO_VALUE + 1,
            EUR: END_INTRADAY_PORTFOLIO_VALUE - 1,
            GBP: END_INTRADAY_PORTFOLIO_VALUE
          }
        })
      ]);

      const ordersConfig = [
        {
          side: "Buy",
          amount: ORDER_BUY_AMOUNT_PRE_REAL_TIME_FEE,
          updatedAt: TODAY
        }
      ];
      const transactionSettledYesterday = await buildAssetTransaction({
        owner: user.id,
        status: "Settled"
      });
      const orderFilledToday = await Promise.all(
        ordersConfig.map(({ side, amount, updatedAt }) =>
          buildOrder({
            status: "Matched",
            providers: {
              wealthkernel: {
                status: "Matched",
                id: faker.string.uuid(),
                submittedAt: updatedAt
              }
            },
            filledAt: updatedAt,
            transaction: transactionSettledYesterday.id,
            side: side as "Buy" | "Sell",
            consideration: {
              amount: amount - REAL_TIME_EXECUTION_FEE,
              originalAmount: amount,
              currency: "GBP"
            },
            fees: {
              realtimeExecution: {
                amount: REAL_TIME_EXECUTION_FEE / 100,
                currency: "GBP"
              },
              fx: {
                amount: 0,
                currency: "GBP"
              }
            },
            updatedAt
          })
        )
      );
      transactionSettledYesterday.orders = orderFilledToday.map((order) => order.id);
      await transactionSettledYesterday.save();
      await transactionSettledYesterday.populate("orders");
    });

    it("should return the correct up-by for today, not taking into account real time execution fees", async () => {
      portfolio = await PortfolioService.getPortfolio(portfolio.id, true); // we need the currectTicker to be populated
      const upBy = await PortfolioService.calculateUpByValues(portfolio, transactions, TenorEnum.TODAY);
      expect(upBy).toEqual({ today: 2 }); // 15 - 10 - 3
    });
  });
});
