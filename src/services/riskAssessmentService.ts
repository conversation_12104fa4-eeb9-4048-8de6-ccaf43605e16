import { captureException } from "@sentry/node";
import { countriesConfig, entitiesConfig } from "@wealthyhood/shared-configs";
import Decimal from "decimal.js";
import {
  amlScreeningScoreMapping,
  employmentRiskMapping,
  riskScoreMapping,
  sourceOfWealthRiskMapping,
  volumeOfTransactionsRiskBands
} from "../configs/riskAssessmentConfig";
import { DEFAULT_SAVINGS_PRODUCT_CONFIG } from "../configs/savingsConfig";
import events from "../event-handlers/events";
import logger from "../external-services/loggerService";
import eventEmitter from "../loaders/eventEmitter";
import { PortfolioDocument } from "../models/Portfolio";
import {
  DueDiligenceClassificationEnum,
  RiskAssessment,
  RiskAssessmentAmlScreening,
  RiskAssessmentDTOInterface,
  RiskAssessmentEmploymentStatus,
  RiskAssessmentNationality,
  RiskAssessmentSourcesOfFunds,
  RiskAssessmentSourcesOfWealth,
  RiskAssessmentVolumeOfTransactions,
  RiskScoreClassificationEnum,
  SourceOfFundsEnum
} from "../models/RiskAssessment";
import { KycStatusEnum, UserDocument, UserPopulationFieldsEnum } from "../models/User";
import DateUtil from "../utils/dateUtil";
import DbUtil from "../utils/dbUtil";
import { getRiskScoreClassification } from "../utils/riskAssessmentUtil";
import BankAccountService from "./bankAccountService";
import PortfolioService from "./portfolioService";
import SavingsProductService from "./savingsProductService";
import UserService from "./userService";

const REQUIRED_INFO_ADDED_AT = new Date("2024-01-11");

const HIGH_RISK_SCORES = [RiskScoreClassificationEnum.HighRisk, RiskScoreClassificationEnum.Prohibited];

export default class RiskAssessmentService {
  /**
   * @description Method for calculating risk assessment for all eligible users.
   */
  public static async createRiskAssessments(): Promise<void> {
    await UserService.getUsersStreamed({ kycStatus: KycStatusEnum.PASSED }, "latestRiskAssessment").eachAsync(
      async (users) => {
        const promises = users.map(async (user) => {
          try {
            const createdAtDate = new Date(user.latestRiskAssessment?.createdAt);
            const { start, end } = DateUtil.getStartAndEndOfToday();

            if (createdAtDate >= start && createdAtDate <= end) {
              return;
            }

            if (user.email.startsWith("deleted")) {
              return;
            }

            await RiskAssessmentService.createRiskAssessment(user);
          } catch (err) {
            captureException(err);
            logger.error(`Failed while creating risk assessment for user ${user.email}`, {
              module: "RiskAssessmentService",
              method: "createRiskAssessments",
              data: { user: user.id }
            });
          }
        });

        await Promise.all(promises);
      },
      { batchSize: 20 }
    );
  }

  public static async createRiskAssessment(user: UserDocument) {
    if (user.kycStatus !== "passed") {
      throw new Error("To calculate risk assessment user must be verified");
    }
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_RISK_ASSESSMENT);
    const latestRiskAssessment = user.latestRiskAssessment;

    const riskAssessment = await RiskAssessmentService._calculateRiskAssessment(user);

    const riskAssessmentDocument = await new RiskAssessment(riskAssessment).save();
    eventEmitter.emit(events.user.riskAssessmentCreated.eventId, user, riskAssessmentDocument);

    const isLastRiskAssessmentHighRisk = HIGH_RISK_SCORES.includes(latestRiskAssessment?.classification);
    const isCurrentRiskAssessmentHighRisk = HIGH_RISK_SCORES.includes(riskAssessmentDocument.classification);

    if (!isLastRiskAssessmentHighRisk && isCurrentRiskAssessmentHighRisk) {
      logger.info(`High risk assessment detected for user ${user.id}`, {
        module: "RiskAssessmentService",
        method: "createRiskAssessment"
      });
      eventEmitter.emit(events.user.highRiskAssessmentDetected.eventId, user);
    }
  }

  private static async _calculateRiskAssessment(user: UserDocument): Promise<RiskAssessmentDTOInterface> {
    const nationality = this._getCountryRiskScore(user, user.companyEntity);
    const employmentStatus = this._getEmploymentStatusRiskScore(user, user.companyEntity);
    const sourcesOfWealth = RiskAssessmentService._getSourceOfWealthRiskScore(user, user.companyEntity);
    const sourcesOfFunds = await RiskAssessmentService._getSourcesOfFundsRiskScore(user);
    const volumeOfTransactions = await RiskAssessmentService._getVolumeOfTransactionsRiskScore(user);
    const amlScreening = RiskAssessmentService._getAMLScreeningRiskScore(user);

    const totalScore =
      nationality.score +
      employmentStatus.score +
      sourcesOfWealth.score +
      sourcesOfFunds.score +
      volumeOfTransactions.score +
      amlScreening.score;

    const riskScoreClassification = getRiskScoreClassification(totalScore);

    let dueDiligenceClassification: DueDiligenceClassificationEnum | undefined;
    if (user.companyEntity !== entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      dueDiligenceClassification = await RiskAssessmentService._getDueDiligenceClassification(
        user,
        riskScoreClassification
      );
    }

    return {
      createdAt: new Date(),
      owner: user._id,
      nationality,
      employmentStatus,
      sourcesOfFunds,
      volumeOfTransactions,
      amlScreening,
      sourcesOfWealth,
      totalScore,
      dueDiligenceClassification
    };
  }

  private static _getCountryRiskScore(
    user: UserDocument,
    entity: entitiesConfig.CompanyEntityEnum
  ): RiskAssessmentNationality {
    // For WEALTHYHOOD_EUROPE we use residency country for country risk scoring, for WEALTHYHOOD_UK we use nationality.
    const countryCode =
      entity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_EUROPE
        ? user.residencyCountry
        : user.nationalities[0];

    return {
      value: countryCode,
      score: riskScoreMapping[entity][countryCode]
    };
  }

  private static _getSourceOfWealthRiskScore(
    user: UserDocument,
    entity: entitiesConfig.CompanyEntityEnum
  ): RiskAssessmentSourcesOfWealth {
    if (RiskAssessmentService._isUserEmploymentInfoMock(user.submittedRequiredInfoAt)) {
      return {
        value: user.employmentInfo.sourcesOfWealth,
        score: 0
      };
    }

    const mapping = sourceOfWealthRiskMapping[entity];
    const highestScoringSourceOfWealth = user.employmentInfo.sourcesOfWealth.reduce(
      (best, current) => (mapping[current] > mapping[best] ? current : best),
      user.employmentInfo.sourcesOfWealth[0]
    );
    return { value: user.employmentInfo.sourcesOfWealth, score: mapping[highestScoringSourceOfWealth] };
  }

  private static _getEmploymentStatusRiskScore(
    user: UserDocument,
    entity: entitiesConfig.CompanyEntityEnum
  ): RiskAssessmentEmploymentStatus {
    if (RiskAssessmentService._isUserEmploymentInfoMock(user.submittedRequiredInfoAt)) {
      return {
        value: user.employmentInfo.employmentStatus,
        score: 0
      };
    }

    return {
      value: user.employmentInfo.employmentStatus,
      score: employmentRiskMapping[entity][user.employmentInfo.employmentStatus]
    };
  }

  private static async _getSourcesOfFundsRiskScore(user: UserDocument): Promise<RiskAssessmentSourcesOfFunds> {
    // For UK users deposits score is always 0
    if (user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK) {
      return {
        value: [SourceOfFundsEnum.LinkedUKBankAccount],
        score: 0
      };
    }

    const bankAccounts = await BankAccountService.getBankAccounts({ owner: user.id });

    // For EU users we check if they have any bank accounts from a non-EU country
    const EU_COUNTRY_CODES = countriesConfig.availableCountries
      .filter((country) => this._isEuropeanCountry(country.code))
      .map((country) => country.code);

    const userHasNonEUOrUKIban = bankAccounts
      .filter((account) => account.iban)
      .some((account) => {
        const countryCode = account.iban.substring(0, 2).toUpperCase() as countriesConfig.CountryCodesType;
        return !EU_COUNTRY_CODES.includes(countryCode) && countryCode !== "GB";
      });

    if (userHasNonEUOrUKIban) {
      return {
        value: [SourceOfFundsEnum.LinkedNonEUBankAccount],
        score: 6
      };
    }

    return {
      value: [SourceOfFundsEnum.LinkedEUBankAccount],
      score: 0
    };
  }

  private static async _getVolumeOfTransactionsRiskScore(
    user: UserDocument
  ): Promise<RiskAssessmentVolumeOfTransactions> {
    await DbUtil.populateIfNotAlreadyPopulated(user, UserPopulationFieldsEnum.LATEST_RISK_ASSESSMENT);

    let volumeOfTransactions: Decimal;

    // If the user has a previous risk assessment we add the existing volumeOfBuyTransactions after that date.
    // If not we calculate it for all the buy transactions
    if (user.latestRiskAssessment) {
      const newVolume = await UserService.getVolumeOfTransactionsForUser(
        user,
        user.latestRiskAssessment.createdAt
      );
      const previousAssessmentVolumeOfTransactions = user.latestRiskAssessment.volumeOfTransactions.value;
      volumeOfTransactions = Decimal.add(previousAssessmentVolumeOfTransactions, new Decimal(newVolume));
    } else {
      volumeOfTransactions = await UserService.getVolumeOfTransactionsForUser(user);
    }

    const bands = volumeOfTransactionsRiskBands[user.companyEntity];
    let score = 0;
    for (const band of bands) {
      if (volumeOfTransactions.gte(band.min) && (band.max === null || volumeOfTransactions.lte(band.max))) {
        score = band.score;
        break;
      }
    }

    return {
      value: volumeOfTransactions.toNumber(),
      score
    };
  }

  private static _getAMLScreeningRiskScore(user: UserDocument): RiskAssessmentAmlScreening {
    let score = amlScreeningScoreMapping[user.companyEntity][user.amlScreening];
    if (typeof score === "function") {
      score = score(user);
    }

    return {
      value: user.amlScreening,
      score
    };
  }

  /**
   * @description Inform us if a user's emplyment data is mocked.
   * This happens when the user submittedRequiredInfoAt date is before we added employment data.
   */
  private static _isUserEmploymentInfoMock(userSubmittedRequiredInfoAt: Date) {
    if (userSubmittedRequiredInfoAt < REQUIRED_INFO_ADDED_AT) {
      return true;
    }
    return false;
  }

  private static async _getDueDiligenceClassification(
    user: UserDocument,
    riskScoreClassification: RiskScoreClassificationEnum
  ): Promise<DueDiligenceClassificationEnum> {
    const portfolio: PortfolioDocument = await PortfolioService.getGeneralInvestmentPortfolio(user, true);
    const { aggregatedSavingsAmount } = await SavingsProductService.getAggregatedSavingsAmount(
      portfolio,
      DEFAULT_SAVINGS_PRODUCT_CONFIG[user.companyEntity]
    );

    const cashValue = portfolio.cash[user.currency]?.available ?? 0;
    const savingsValue = aggregatedSavingsAmount;
    const holdingsValue = portfolio.getCalculatedPrice(portfolio.holdings.map((holding) => holding.asset));
    const accountValue = new Decimal(cashValue).add(savingsValue).add(holdingsValue).toNumber();

    if (riskScoreClassification === RiskScoreClassificationEnum.Prohibited) {
      return DueDiligenceClassificationEnum.Blocked;
    }

    if (accountValue < 10000) {
      if (
        riskScoreClassification === RiskScoreClassificationEnum.LowRisk ||
        riskScoreClassification === RiskScoreClassificationEnum.MediumRisk
      ) {
        return DueDiligenceClassificationEnum.Simplified;
      }
      return DueDiligenceClassificationEnum.Standard;
    }

    if (accountValue < 75000) {
      return DueDiligenceClassificationEnum.Standard;
    }

    if (
      riskScoreClassification === RiskScoreClassificationEnum.LowRisk ||
      riskScoreClassification === RiskScoreClassificationEnum.MediumRisk
    ) {
      return DueDiligenceClassificationEnum.Standard;
    }
    return DueDiligenceClassificationEnum.Enhanced;
  }

  private static _isEuropeanCountry(countryCode: countriesConfig.CountryCodesType): boolean {
    const belongsToEuropeanRegion = ["EU", "GREECE"].some(
      (region) => countriesConfig.COUNTRIES_REGIONS_MAPPING[countryCode] === region
    );
    const isCyprus = countryCode === "CY"; // Cyprus is in the EU but we currently treat it as non-EU (OTHER)

    return belongsToEuropeanRegion || isCyprus;
  }
}
