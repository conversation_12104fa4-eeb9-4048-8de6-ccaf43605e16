import {
  APP_NOTIFICATION_CATEGORY_CONFIG,
  APP_NOTIFICATION_SETTINGS_CONFIG,
  EMAIL_NOTIFICATION_CATEGORY_CONFIG,
  EMAIL_NOTIFICATION_SETTINGS_CONFIG,
  EMAIL_NOTIFICATIONS_CONFIG
} from "../configs/notificationSettingsConfig";
import { UserDocument } from "../models/User";
import {
  AppNotificationSettingEnum,
  EmailNotificationSettingEnum,
  NotificationSettings,
  NotificationSettingsDocument
} from "../models/NotificationSettings";
import eventEmitter from "../loaders/eventEmitter";
import events from "../event-handlers/events";
import DbUtil from "../utils/dbUtil";
import { NotificationSettingsFilter } from "filters";
import { envIsProd } from "../utils/environmentUtil";
import { EmailType } from "../external-services/mailerService";

type NotificationSettingsCategory<T> = {
  category: string;
  notifications: {
    id: T;
    name: string;
    description: string;
    active: boolean;
  }[];
};

type NotificationSettingsResponseType = {
  app?: NotificationSettingsCategory<AppNotificationSettingEnum>[];
  email?: NotificationSettingsCategory<EmailNotificationSettingEnum>[];
};

export default class NotificationSettingsService {
  private static readonly TYPES_OF_EMAIL_SENT_REGARDLESS_OF_SETTINGS: Set<EmailType> = new Set([
    "deletionSuccess",
    "deletionSuccessForInactiveUser"
  ]);

  public static hasAllowedAppNotification(user: UserDocument, id: AppNotificationSettingEnum): boolean {
    if (!user.populated("notificationSettings")) {
      throw new Error("notificationSettings must be populated on user");
    }
    const notificationSettings = user.notificationSettings as NotificationSettingsDocument;
    return notificationSettings.app.deviceNotificationsEnabled && notificationSettings.app.settings.get(id);
  }

  public static async hasAllowedEmailNotification(user: UserDocument, emailType: EmailType): Promise<boolean> {
    const isTypeOfEmailSentRegardlessOfSetttings =
      NotificationSettingsService.TYPES_OF_EMAIL_SENT_REGARDLESS_OF_SETTINGS.has(emailType);

    if (isTypeOfEmailSentRegardlessOfSetttings) return true;

    const notificationSettings = await NotificationSettings.findOne({ owner: user.id });

    const id: EmailNotificationSettingEnum = EMAIL_NOTIFICATIONS_CONFIG[emailType];
    return notificationSettings.email.settings.get(id);
  }

  public static async createDefaultNotificationSettingsForUser(
    user: UserDocument
  ): Promise<NotificationSettingsDocument> {
    return new NotificationSettings({
      owner: user._id,
      app: {
        deviceNotificationsEnabled: ["ios", "android"].includes(user.lastLoginPlatform),
        settings: new Map(Object.values(AppNotificationSettingEnum).map((setting) => [setting, true]))
      },
      email: {
        settings: new Map(Object.values(EmailNotificationSettingEnum).map((setting) => [setting, true]))
      }
    }).save();
  }

  public static async getNotificationsSettings(user: UserDocument): Promise<NotificationSettingsResponseType> {
    const notificationSettings = await NotificationSettings.findOne({ owner: user.id });

    if (!notificationSettings) {
      return {};
    }

    const appSettings = NotificationSettingsService._getAppNotificationSettings(notificationSettings);
    const emailSettings = NotificationSettingsService._getEmailNotificationSettings(notificationSettings);

    return {
      app: appSettings,
      email: emailSettings
    };
  }

  /**
   * @description Returns a cursor to iterate through all notification settings.
   */
  public static getNotificationSettingsStreamed(
    filter: NotificationSettingsFilter = {},
    populate: {
      owner?: boolean;
    } = {}
  ) {
    const actualFilter = NotificationSettingsService._createDbFilter(filter);
    const populateString = DbUtil.getPopulationString(populate);

    return NotificationSettings.find(actualFilter)
      .populate(populateString)
      .cursor()
      .addCursorFlag("noCursorTimeout", envIsProd());
  }

  public static async updateDeviceNotificationSettings(
    user: UserDocument,
    deviceNotificationsEnabled: boolean
  ): Promise<void> {
    // If the user chose to **disable** notifications while onboarding, we set all app settings to false.
    if (!deviceNotificationsEnabled) {
      const settings = new Map(Object.values(AppNotificationSettingEnum).map((setting) => [setting, false]));

      await NotificationSettings.findOneAndUpdate(
        { owner: user._id },
        {
          app: {
            deviceNotificationsEnabled: deviceNotificationsEnabled,
            settings
          }
        }
      );
    } else {
      await NotificationSettings.findOneAndUpdate(
        { owner: user._id },
        {
          "app.deviceNotificationsEnabled": deviceNotificationsEnabled
        }
      );
    }
  }

  public static async updateNotificationSetting(
    user: UserDocument,
    id: AppNotificationSettingEnum | EmailNotificationSettingEnum,
    active: boolean
  ): Promise<NotificationSettingsResponseType> {
    let notificationSettings: NotificationSettingsDocument;

    if (NotificationSettingsService._isAppNotificationSetting(id)) {
      notificationSettings = await NotificationSettingsService._updateAppNotificationSetting(user, id, active);
    } else if (NotificationSettingsService._isEmailNotificationSetting(id)) {
      notificationSettings = await NotificationSettingsService._updateEmailNotificationSetting(user, id, active);
    } else {
      throw new Error("Invalid notification ID");
    }

    const appSettings = NotificationSettingsService._getAppNotificationSettings(notificationSettings);
    const emailSettings = NotificationSettingsService._getEmailNotificationSettings(notificationSettings);

    return {
      app: appSettings,
      email: emailSettings
    };
  }

  public static async disableAllNotifications(user: UserDocument): Promise<void> {
    const appSettings = new Map(Object.values(AppNotificationSettingEnum).map((setting) => [setting, false]));
    const emailSettings = new Map(Object.values(EmailNotificationSettingEnum).map((setting) => [setting, false]));

    await NotificationSettings.findOneAndUpdate(
      { owner: user._id },
      {
        app: {
          settings: appSettings
        },
        email: {
          settings: emailSettings
        }
      }
    );
  }

  private static _getAppNotificationSettings(
    notificationSettings: NotificationSettingsDocument
  ): NotificationSettingsCategory<AppNotificationSettingEnum>[] {
    return Object.entries(APP_NOTIFICATION_CATEGORY_CONFIG).map(([, categoryConfig]) => ({
      category: categoryConfig.name,
      notifications: categoryConfig.notifications.map((settingEnum) => ({
        id: settingEnum,
        name: APP_NOTIFICATION_SETTINGS_CONFIG[settingEnum].name,
        description: APP_NOTIFICATION_SETTINGS_CONFIG[settingEnum].description,
        active: notificationSettings.app.settings.get(settingEnum) || false
      }))
    }));
  }

  private static _getEmailNotificationSettings(
    notificationSettings: NotificationSettingsDocument
  ): NotificationSettingsCategory<EmailNotificationSettingEnum>[] {
    return Object.entries(EMAIL_NOTIFICATION_CATEGORY_CONFIG).map(([, categoryConfig]) => ({
      category: categoryConfig.name,
      notifications: categoryConfig.notifications.map((settingEnum) => ({
        id: settingEnum,
        name: EMAIL_NOTIFICATION_SETTINGS_CONFIG[settingEnum].name,
        description: EMAIL_NOTIFICATION_SETTINGS_CONFIG[settingEnum].description,
        active: notificationSettings.email.settings.get(settingEnum) || false
      }))
    }));
  }

  private static async _updateAppNotificationSetting(
    owner: UserDocument,
    notificationId: AppNotificationSettingEnum,
    active: boolean
  ): Promise<NotificationSettingsDocument> {
    // If the user has just enabled an app notification, we should also update device notifications
    // to be active regardless of its previous value.
    const deviceNotificationSetting = active ? { "app.deviceNotificationsEnabled": true } : {};

    return NotificationSettings.findOneAndUpdate(
      { owner: owner._id },
      {
        [`app.settings.${notificationId}`]: active,
        ...deviceNotificationSetting
      },
      {
        new: true
      }
    );
  }

  private static async _updateEmailNotificationSetting(
    user: UserDocument,
    notificationId: EmailNotificationSettingEnum,
    active: boolean
  ): Promise<NotificationSettingsDocument> {
    if (notificationId === EmailNotificationSettingEnum.WEALTHYBITES) {
      eventEmitter.emit(events.user.wealthybitesSubscription.eventId, user, { enabled: active });
    } else if (notificationId === EmailNotificationSettingEnum.PROMOTIONAL) {
      eventEmitter.emit(events.user.promotionalEmailSubscription.eventId, user, { enabled: active });
    }

    return NotificationSettings.findOneAndUpdate(
      { owner: user._id },
      {
        [`email.settings.${notificationId}`]: active
      },
      {
        new: true
      }
    );
  }

  private static _isAppNotificationSetting(
    id: AppNotificationSettingEnum | EmailNotificationSettingEnum
  ): id is AppNotificationSettingEnum {
    return Object.values(AppNotificationSettingEnum).includes(id as AppNotificationSettingEnum);
  }

  private static _isEmailNotificationSetting(
    id: AppNotificationSettingEnum | EmailNotificationSettingEnum
  ): id is EmailNotificationSettingEnum {
    return Object.values(EmailNotificationSettingEnum).includes(id as EmailNotificationSettingEnum);
  }

  private static _createDbFilter(filter: NotificationSettingsFilter) {
    const dbFilter: any = {};

    if (filter.hasEnabledDeviceNotifications) {
      dbFilter["app.deviceNotificationsEnabled"] = true;
    }

    if (filter.hasEnabledAppNotificationSetting) {
      dbFilter[`app.settings.${filter.hasEnabledAppNotificationSetting}`] = true;
    }

    return dbFilter
      ? Object.fromEntries(
          Object.entries(dbFilter).filter(
            ([key, value]) => key != "status" && value !== undefined && value !== null
          )
        )
      : {};
  }
}
