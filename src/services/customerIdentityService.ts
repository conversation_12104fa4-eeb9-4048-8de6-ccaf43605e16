import { 
  CustomerIdentity, 
  CustomerIdentityDocument, 
  CustomerIdentityDTOInterface,
  IdentityDocumentInterface,
  IdentityVerificationStatusType
} from "../models/CustomerIdentity";
import { UserDocument } from "../models/User";
import { PassportDetails } from "./sumsubBasedKycService";
import SlackService from "../external-services/slackService";
import logger from "../external-services/loggerService";
import { BadRequestError, NotFoundError } from "../models/ApiErrors";
import { countriesConfig } from "@wealthyhood/shared-configs";

export default class CustomerIdentityService {
  /**
   * Create or update customer identity from KYC passport details
   */
  public static async saveCustomerIdentity(
    user: UserDocument,
    passportDetails: PassportDetails,
    verificationProvider: string = "SUMSUB"
  ): Promise<CustomerIdentityDocument> {
    logger.info(`Saving customer identity for user ${user._id}`, {
      module: "CustomerIdentityService",
      method: "saveCustomerIdentity",
      userEmail: user.email
    });

    // Convert passport details to identity documents
    const documents: IdentityDocumentInterface[] = passportDetails.idDocs.map(doc => ({
      documentType: doc.idDocType as any,
      issuingCountry: doc.country as countriesConfig.CountryCodesType,
      mrzLine1: doc.mrzLine1,
      isOldFormat: this._isOldGreekIdFormat(doc)
    }));

    // Determine if manual review is required
    const requiresManualReview = this._requiresManualReview(documents);
    
    // Determine verification status
    const verificationStatus: IdentityVerificationStatusType = requiresManualReview 
      ? "MANUAL_REVIEW" 
      : "VERIFIED";

    const identityData: Partial<CustomerIdentityDTOInterface> = {
      owner: user._id,
      firstName: passportDetails.firstName,
      lastName: passportDetails.lastName,
      dateOfBirth: passportDetails.dateOfBirth,
      nationality: passportDetails.nationality,
      documents,
      verificationStatus,
      verificationProvider,
      verificationDate: new Date(),
      requiresManualReview,
      slackNotificationSent: false
    };

    // Create or update customer identity
    const customerIdentity = await CustomerIdentity.findOneAndUpdate(
      { owner: user._id },
      identityData,
      {
        upsert: true,
        new: true,
        runValidators: true
      }
    );

    // Check if Slack notification should be sent for old Greek ID
    await this._handleOldGreekIdNotification(user, customerIdentity);

    logger.info(`Customer identity saved successfully for user ${user._id}`, {
      module: "CustomerIdentityService",
      method: "saveCustomerIdentity",
      userEmail: user.email,
      verificationStatus: customerIdentity.verificationStatus,
      requiresManualReview: customerIdentity.requiresManualReview
    });

    return customerIdentity;
  }

  /**
   * Get customer identity by user ID
   */
  public static async getCustomerIdentity(userId: string): Promise<CustomerIdentityDocument | null> {
    return CustomerIdentity.findOne({ owner: userId });
  }

  /**
   * Get customer identity by user ID or throw error if not found
   */
  public static async getCustomerIdentityOrThrow(userId: string): Promise<CustomerIdentityDocument> {
    const identity = await this.getCustomerIdentity(userId);
    if (!identity) {
      throw new NotFoundError("Customer identity not found");
    }
    return identity;
  }

  /**
   * Update verification status
   */
  public static async updateVerificationStatus(
    userId: string,
    status: IdentityVerificationStatusType,
    notes?: string
  ): Promise<CustomerIdentityDocument> {
    const identity = await CustomerIdentity.findOneAndUpdate(
      { owner: userId },
      { 
        verificationStatus: status,
        ...(notes && { notes }),
        verificationDate: new Date()
      },
      { new: true, runValidators: true }
    );

    if (!identity) {
      throw new NotFoundError("Customer identity not found");
    }

    logger.info(`Customer identity verification status updated`, {
      module: "CustomerIdentityService",
      method: "updateVerificationStatus",
      userId,
      status,
      notes
    });

    return identity;
  }

  /**
   * Get all customers requiring manual review
   */
  public static async getCustomersRequiringManualReview(): Promise<CustomerIdentityDocument[]> {
    return CustomerIdentity.find({ requiresManualReview: true }).populate("owner");
  }

  /**
   * Get all customers with old Greek IDs
   */
  public static async getCustomersWithOldGreekIds(): Promise<CustomerIdentityDocument[]> {
    return CustomerIdentity.find({
      "documents.issuingCountry": "GRC",
      "documents.documentType": "ID_CARD",
      "documents.isOldFormat": true
    }).populate("owner");
  }

  /**
   * Mark Slack notification as sent
   */
  public static async markSlackNotificationSent(userId: string): Promise<void> {
    await CustomerIdentity.findOneAndUpdate(
      { owner: userId },
      { slackNotificationSent: true }
    );
  }

  /**
   * Check if document is old Greek ID format
   */
  private static _isOldGreekIdFormat(doc: any): boolean {
    return (
      doc.country === "GRC" &&
      doc.idDocType === "ID_CARD" &&
      !doc.mrzLine1 // Old format doesn't have MRZ
    );
  }

  /**
   * Check if manual review is required
   */
  private static _requiresManualReview(documents: IdentityDocumentInterface[]): boolean {
    return documents.some(doc => 
      doc.issuingCountry === "GRC" && 
      doc.documentType === "ID_CARD" && 
      doc.isOldFormat
    );
  }

  /**
   * Handle Slack notification for old Greek ID
   */
  private static async _handleOldGreekIdNotification(
    user: UserDocument,
    customerIdentity: CustomerIdentityDocument
  ): Promise<void> {
    // Check if user has old Greek ID and notification hasn't been sent
    if (customerIdentity.hasOldGreekId && !customerIdentity.slackNotificationSent) {
      try {
        await SlackService.notifyOldGreekIdentity(user, customerIdentity);
        await this.markSlackNotificationSent(user._id.toString());
        
        logger.info(`Slack notification sent for old Greek ID`, {
          module: "CustomerIdentityService",
          method: "_handleOldGreekIdNotification",
          userEmail: user.email,
          userId: user._id.toString()
        });
      } catch (error) {
        logger.error(`Failed to send Slack notification for old Greek ID`, {
          module: "CustomerIdentityService",
          method: "_handleOldGreekIdNotification",
          userEmail: user.email,
          userId: user._id.toString(),
          error: error.message
        });
        // Don't throw error - notification failure shouldn't break the flow
      }
    }
  }

  /**
   * Validate identity document data
   */
  public static validateIdentityDocument(document: IdentityDocumentInterface): boolean {
    if (!document.documentType || !document.issuingCountry) {
      return false;
    }

    // Additional validation logic can be added here
    return true;
  }

  /**
   * Get identity verification statistics
   */
  public static async getVerificationStatistics(): Promise<{
    total: number;
    verified: number;
    pending: number;
    failed: number;
    manualReview: number;
    oldGreekIds: number;
  }> {
    const [
      total,
      verified,
      pending,
      failed,
      manualReview,
      oldGreekIds
    ] = await Promise.all([
      CustomerIdentity.countDocuments(),
      CustomerIdentity.countDocuments({ verificationStatus: "VERIFIED" }),
      CustomerIdentity.countDocuments({ verificationStatus: "PENDING" }),
      CustomerIdentity.countDocuments({ verificationStatus: "FAILED" }),
      CustomerIdentity.countDocuments({ verificationStatus: "MANUAL_REVIEW" }),
      CustomerIdentity.countDocuments({
        "documents.issuingCountry": "GRC",
        "documents.documentType": "ID_CARD",
        "documents.isOldFormat": true
      })
    ]);

    return {
      total,
      verified,
      pending,
      failed,
      manualReview,
      oldGreekIds
    };
  }
}
