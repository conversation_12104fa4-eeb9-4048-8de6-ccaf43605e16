import { currenciesConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import {
  buildCreditTicket,
  buildDepositCashTransaction,
  buildPortfolio,
  buildUser
} from "../../tests/utils/generateModels";
import { CreditTicketStatusType } from "../CreditTicket";
import { DepositCashTransactionDocument } from "../Transaction";
import { UserDocument } from "../User";
import { PortfolioDocument } from "../Portfolio";
import { DepositMethodEnum } from "../../types/transactions";

describe("Transaction", () => {
  beforeAll(async () => await connectDb("Transaction"));
  afterEach(async () => await clearDb());
  afterAll(async () => await closeDb());

  // Helper to create a base deposit with defaults
  const createBaseDepositData = async (overrides: Partial<DepositCashTransactionDocument> = {}) => {
    const user = await buildUser({}, false);
    const portfolio = await buildPortfolio({ owner: user._id });

    const defaults = {
      owner: user._id,
      portfolio: portfolio._id,
      depositMethod: DepositMethodEnum.OPEN_BANKING,
      consideration: {
        currency: "EUR" as currenciesConfig.MainCurrencyType,
        amount: 10000 // €100 in cents
      },
      activeProviders: [ProviderEnum.WEALTHKERNEL],
      status: "Pending" as const,
      ...overrides
    };

    return defaults;
  };

  describe("deposit.inInstantMoneyFlow", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return false when deposit has no linked credit ticket", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined // No credit ticket
      });

      const deposit = await buildDepositCashTransaction(depositData);
      expect(deposit.inInstantMoneyFlow).toBe(false);
    });

    it("should return true when linked credit ticket status is 'Credited'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should return true when linked credit ticket status is 'Settled'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Settled" as CreditTicketStatusType,
        creditedAt: new Date(),
        settledAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should return false when linked credit ticket status is 'Rejected'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Rejected" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBe(false);
    });

    it("should return undefined when linked credit ticket status is 'Pending'", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.inInstantMoneyFlow).toBeUndefined();
    });

    it("should handle credit ticket transitions from Pending to Credited", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially pending, should return undefined
      expect(deposit.inInstantMoneyFlow).toBeUndefined();

      // Update credit ticket to Credited
      creditTicket.status = "Credited";
      creditTicket.creditedAt = new Date();
      await creditTicket.save();

      // Re-populate and check again
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should handle credit ticket transitions from Credited to Settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially credited, should return true
      expect(deposit.inInstantMoneyFlow).toBe(true);

      // Update credit ticket to Settled
      creditTicket.status = "Settled";
      creditTicket.settledAt = new Date();
      await creditTicket.save();

      // Re-populate and check again - should still be true
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(true);
    });

    it("should handle credit ticket transitions from Pending to Rejected", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      // Initially pending, should return undefined
      expect(deposit.inInstantMoneyFlow).toBeUndefined();

      // Update credit ticket to Rejected
      creditTicket.status = "Rejected";
      await creditTicket.save();

      // Re-populate and check again
      await deposit.populate("linkedCreditTicket");
      expect(deposit.inInstantMoneyFlow).toBe(false);
    });
  });

  describe("deposit.displayDate", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;

    beforeEach(async () => {
      user = await buildUser({}, false);
      portfolio = await buildPortfolio({ owner: user._id });
    });

    it("should return credit ticket's credited date as display date if a credit ticket exists and is credited", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Credited" as CreditTicketStatusType,
        creditedAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(creditTicket.creditedAt);
    });

    it("should return credit ticket's credited date as display date if a credit ticket exists and is settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Settled" as CreditTicketStatusType,
        creditedAt: new Date(),
        settledAt: new Date()
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(creditTicket.creditedAt);
    });

    it("should return transaction's settled date as display date if a credit ticket exists, it is not credited/settled, and the transaction has been settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Rejected" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id,
        settledAt: new Date()
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(deposit.settledAt);
    });

    it("should return transaction's created date as display date if a credit ticket exists, it is not credited/settled, and the transaction has not been settled", async () => {
      const creditTicket = await buildCreditTicket({
        owner: user._id,
        portfolio: portfolio._id,
        status: "Pending" as CreditTicketStatusType
      });

      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: creditTicket._id
      });

      const deposit = await buildDepositCashTransaction(depositData);
      await deposit.populate("linkedCreditTicket");

      expect(deposit.displayDate).toEqual(deposit.createdAt);
    });

    it("should return transaction's created date as display date if a credit ticket does not exist exist and the transaction has not been settled", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.displayDate).toEqual(deposit.createdAt);
    });

    it("should return transaction's settled date as display date if a credit ticket does not exist exist and the transaction has been settled", async () => {
      const depositData = await createBaseDepositData({
        owner: user._id,
        portfolio: portfolio._id,
        linkedCreditTicket: undefined,
        settledAt: new Date()
      });

      const deposit = await buildDepositCashTransaction(depositData);

      expect(deposit.displayDate).toEqual(deposit.settledAt);
    });
  });
});
