import mongoose, { Document } from "mongoose";
import { countriesConfig } from "@wealthyhood/shared-configs";
import { UserDocument } from "./User";

/**
 * TYPES
 */
export const IdentityDocumentTypeArray = ["PASSPORT", "ID_CARD", "DRIVING_LICENSE"] as const;
export type IdentityDocumentType = (typeof IdentityDocumentTypeArray)[number];

export const IdentityVerificationStatusArray = ["PENDING", "VERIFIED", "FAILED", "MANUAL_REVIEW"] as const;
export type IdentityVerificationStatusType = (typeof IdentityVerificationStatusArray)[number];

/**
 * INTERFACES
 */
export interface IdentityDocumentInterface {
  documentType: IdentityDocumentType;
  documentNumber?: string;
  issuingCountry: countriesConfig.CountryCodesType;
  expiryDate?: Date;
  issueDate?: Date;
  mrzLine1?: string; // Machine Readable Zone line 1 - indicates new format for Greek IDs
  mrzLine2?: string; // Machine Readable Zone line 2
  isOldFormat?: boolean; // Specifically for Greek ID cards - true if old format detected
}

export interface CustomerIdentityDTOInterface {
  owner: mongoose.Types.ObjectId;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  nationality?: countriesConfig.CountryCodesType;
  documents: IdentityDocumentInterface[];
  verificationStatus: IdentityVerificationStatusType;
  verificationProvider?: string; // e.g., "SUMSUB", "JUMIO"
  verificationDate?: Date;
  requiresManualReview?: boolean;
  slackNotificationSent?: boolean; // Track if Slack notification was sent for old Greek ID
  notes?: string; // Additional notes for manual review
}

export interface CustomerIdentityInterface extends Omit<CustomerIdentityDTOInterface, "owner"> {
  owner: mongoose.Types.ObjectId | UserDocument;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomerIdentityDocument extends CustomerIdentityInterface, Document {}

/**
 * SCHEMA
 */
const identityDocumentSchema = new mongoose.Schema(
  {
    documentType: {
      type: String,
      enum: IdentityDocumentTypeArray,
      required: true
    },
    documentNumber: { type: String },
    issuingCountry: {
      type: String,
      enum: countriesConfig.countryCodesArray,
      required: true
    },
    expiryDate: { type: Date },
    issueDate: { type: Date },
    mrzLine1: { type: String },
    mrzLine2: { type: String },
    isOldFormat: { type: Boolean, default: false }
  },
  { _id: false }
);

const customerIdentitySchema = new mongoose.Schema(
  {
    owner: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
      unique: true // One identity record per user
    },
    firstName: { type: String, trim: true },
    lastName: { type: String, trim: true },
    dateOfBirth: { type: Date },
    nationality: {
      type: String,
      enum: countriesConfig.countryCodesArray
    },
    documents: [identityDocumentSchema],
    verificationStatus: {
      type: String,
      enum: IdentityVerificationStatusArray,
      default: "PENDING"
    },
    verificationProvider: { type: String },
    verificationDate: { type: Date },
    requiresManualReview: { type: Boolean, default: false },
    slackNotificationSent: { type: Boolean, default: false },
    notes: { type: String }
  },
  { toJSON: { virtuals: true }, toObject: { virtuals: true }, timestamps: true }
);

/**
 * INDEXES
 */
customerIdentitySchema.index({ owner: 1 });
customerIdentitySchema.index({ verificationStatus: 1 });
customerIdentitySchema.index({ requiresManualReview: 1 });
customerIdentitySchema.index({ "documents.issuingCountry": 1 });
customerIdentitySchema.index({ "documents.isOldFormat": 1 });

/**
 * VIRTUALS
 */
customerIdentitySchema.virtual("fullName").get(function (): string {
  const identity = this as CustomerIdentityDocument;
  if (identity.firstName && identity.lastName) {
    return `${identity.firstName} ${identity.lastName}`;
  }
  return identity.firstName || identity.lastName || "";
});

customerIdentitySchema.virtual("hasOldGreekId").get(function (): boolean {
  const identity = this as CustomerIdentityDocument;
  return identity.documents.some(
    (doc) => doc.issuingCountry === "GRC" && doc.documentType === "ID_CARD" && doc.isOldFormat
  );
});

customerIdentitySchema.virtual("greekIdDocuments").get(function (): IdentityDocumentInterface[] {
  const identity = this as CustomerIdentityDocument;
  return identity.documents.filter(
    (doc) => doc.issuingCountry === "GRC" && doc.documentType === "ID_CARD"
  );
});

export const CustomerIdentity = mongoose.model<CustomerIdentityDocument>(
  "CustomerIdentity",
  customerIdentitySchema
);
