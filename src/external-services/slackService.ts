import axios, { AxiosRequestConfig } from "axios";
import { UserDocument } from "../models/User";
import { CustomerIdentityDocument } from "../models/CustomerIdentity";
import logger from "./loggerService";
import { envIsProd } from "../utils/envUtil";

export interface SlackMessageAttachment {
  color?: string;
  title?: string;
  text?: string;
  fields?: Array<{
    title: string;
    value: string;
    short?: boolean;
  }>;
  footer?: string;
  ts?: number;
}

export interface SlackMessage {
  text?: string;
  channel?: string;
  username?: string;
  icon_emoji?: string;
  attachments?: SlackMessageAttachment[];
}

export default class SlackService {
  private static readonly WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;
  private static readonly IDENTITY_VERIFICATION_CHANNEL = process.env.SLACK_IDENTITY_VERIFICATION_CHANNEL || "#identity-verification";

  /**
   * Send a notification to <PERSON>lack when an old Greek identity document is detected
   */
  public static async notifyOldGreekIdentity(
    user: UserDocument,
    customerIdentity: CustomerIdentityDocument
  ): Promise<void> {
    if (!envIsProd()) {
      logger.info("Skipping Slack notification in non-production environment", {
        module: "SlackService",
        method: "notifyOldGreekIdentity",
        userEmail: user.email
      });
      return;
    }

    if (!this.WEBHOOK_URL) {
      logger.warn("Slack webhook URL not configured", {
        module: "SlackService",
        method: "notifyOldGreekIdentity"
      });
      return;
    }

    const oldGreekDocs = customerIdentity.greekIdDocuments.filter(doc => doc.isOldFormat);
    
    if (oldGreekDocs.length === 0) {
      return;
    }

    const message: SlackMessage = {
      channel: this.IDENTITY_VERIFICATION_CHANNEL,
      username: "Identity Verification Bot",
      icon_emoji: ":warning:",
      text: "🚨 Old Greek Identity Document Detected",
      attachments: [
        {
          color: "warning",
          title: "Customer Using Old Greek ID Format",
          fields: [
            {
              title: "User Email",
              value: user.email,
              short: true
            },
            {
              title: "User ID",
              value: user._id.toString(),
              short: true
            },
            {
              title: "Full Name",
              value: customerIdentity.fullName || "N/A",
              short: true
            },
            {
              title: "Nationality",
              value: customerIdentity.nationality || "N/A",
              short: true
            },
            {
              title: "Verification Status",
              value: customerIdentity.verificationStatus,
              short: true
            },
            {
              title: "Verification Provider",
              value: customerIdentity.verificationProvider || "N/A",
              short: true
            },
            {
              title: "Document Details",
              value: oldGreekDocs.map(doc => 
                `Type: ${doc.documentType}, Country: ${doc.issuingCountry}, Old Format: ${doc.isOldFormat ? 'Yes' : 'No'}`
              ).join('\n'),
              short: false
            },
            {
              title: "Action Required",
              value: "Manual review required for old Greek ID format verification",
              short: false
            }
          ],
          footer: "Wealthyhood Identity Verification System",
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };

    try {
      await this.sendMessage(message);
      
      logger.info("Slack notification sent for old Greek identity", {
        module: "SlackService",
        method: "notifyOldGreekIdentity",
        userEmail: user.email,
        userId: user._id.toString()
      });
    } catch (error) {
      logger.error("Failed to send Slack notification for old Greek identity", {
        module: "SlackService",
        method: "notifyOldGreekIdentity",
        userEmail: user.email,
        userId: user._id.toString(),
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Send a general identity verification alert to Slack
   */
  public static async notifyIdentityVerificationIssue(
    user: UserDocument,
    issue: string,
    details?: Record<string, any>
  ): Promise<void> {
    if (!envIsProd()) {
      logger.info("Skipping Slack notification in non-production environment", {
        module: "SlackService",
        method: "notifyIdentityVerificationIssue",
        userEmail: user.email
      });
      return;
    }

    if (!this.WEBHOOK_URL) {
      logger.warn("Slack webhook URL not configured", {
        module: "SlackService",
        method: "notifyIdentityVerificationIssue"
      });
      return;
    }

    const message: SlackMessage = {
      channel: this.IDENTITY_VERIFICATION_CHANNEL,
      username: "Identity Verification Bot",
      icon_emoji: ":exclamation:",
      text: "⚠️ Identity Verification Issue",
      attachments: [
        {
          color: "danger",
          title: "Identity Verification Alert",
          fields: [
            {
              title: "User Email",
              value: user.email,
              short: true
            },
            {
              title: "User ID",
              value: user._id.toString(),
              short: true
            },
            {
              title: "Issue",
              value: issue,
              short: false
            },
            ...(details ? [
              {
                title: "Details",
                value: JSON.stringify(details, null, 2),
                short: false
              }
            ] : [])
          ],
          footer: "Wealthyhood Identity Verification System",
          ts: Math.floor(Date.now() / 1000)
        }
      ]
    };

    try {
      await this.sendMessage(message);
      
      logger.info("Slack notification sent for identity verification issue", {
        module: "SlackService",
        method: "notifyIdentityVerificationIssue",
        userEmail: user.email,
        userId: user._id.toString(),
        issue
      });
    } catch (error) {
      logger.error("Failed to send Slack notification for identity verification issue", {
        module: "SlackService",
        method: "notifyIdentityVerificationIssue",
        userEmail: user.email,
        userId: user._id.toString(),
        issue,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Send a message to Slack using the webhook URL
   */
  private static async sendMessage(message: SlackMessage): Promise<void> {
    if (!this.WEBHOOK_URL) {
      throw new Error("Slack webhook URL not configured");
    }

    const config: AxiosRequestConfig = {
      method: "POST",
      url: this.WEBHOOK_URL,
      headers: {
        "Content-Type": "application/json"
      },
      data: message
    };

    const response = await axios(config);

    if (response.status !== 200) {
      throw new Error(`Slack API returned status ${response.status}: ${response.statusText}`);
    }
  }
}
